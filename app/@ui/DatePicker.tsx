import { useState } from "react";
import { EventOutlined } from "@mui/icons-material";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { Button } from "~/@shadcn/ui/button";
import { FormControl, FormField, FormLabel } from "~/@shadcn/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "~/@shadcn/ui/popover";
import { Calendar } from "~/@shadcn/ui/calendar";
import { cn } from "~/@shadcn/utils";
import { Separator } from "~/@shadcn/ui/separator";

// Exports
type DatePickerProps = {
  id: string;
  name: string;
  label: string;
  date?: Date;
  onSelect?: (nextDate: Date | undefined) => void;
};
export const DatePicker = ({
  date,
  id,
  label,
  name,
  onSelect,
}: DatePickerProps) => {
  const [open, setOpen] = useState<boolean>();
  return (
    <FormField id={id} name={name}>
      <FormLabel>{label}</FormLabel>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button variant="outline" className="h-auto rounded-2xl p-3">
              <EventOutlined className="h-6 w-6" />
              <span
                className={cn(
                  "grow text-start font-normal",
                  !date && "text-secondary"
                )}
              >
                {date ? format(date, "MM/dd/yyyy") : "Pick a date"}
              </span>
              <CaretSortIcon className="h-6 w-6" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            className="pb-0"
            mode="single"
            selected={date}
            onSelect={(nextDate) => {
              onSelect?.(nextDate);
              setOpen(false);
            }}
          />
          <Separator />
          <div className="flex justify-end px-3 py-2">
            <Button
              variant="ghost-destructive"
              onClick={() => {
                onSelect?.(undefined);
                setOpen(false);
              }}
            >
              Clear
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </FormField>
  );
};
