import { useCallback, useEffect, useMemo, useState } from "react";
import { AccessTimeOutlined } from "@mui/icons-material";
import { CaretSortIcon } from "@radix-ui/react-icons";
import { format } from "date-fns";
import { Button } from "~/@shadcn/ui/button";
import { FormControl, FormField, FormLabel } from "~/@shadcn/ui/form";
import { Popover, PopoverContent, PopoverTrigger } from "~/@shadcn/ui/popover";
import { cn } from "~/@shadcn/utils";
import { Separator } from "~/@shadcn/ui/separator";
import {
  Period,
  dateToTimeParts,
  hoursPeriodToHours24,
  timePartsToDate,
} from "~/utils/time";

// Helpers
const useTimeParts = (initialDate?: Date) => {
  const [timeParts, setTimeParts] = useState(() =>
    dateToTimeParts(initialDate ?? new Date())
  );

  const setHours = useCallback((hours: number) => {
    setTimeParts((prev) => ({
      ...prev,
      hours,
      hours24: hoursPeriodToHours24(hours, prev.period),
    }));
  }, []);
  const setMinutes = useCallback((minutes: number) => {
    setTimeParts((prev) => ({ ...prev, minutes }));
  }, []);
  const setPeriod = useCallback((period: Period) => {
    setTimeParts((prev) => ({
      ...prev,
      period,
      hours24: hoursPeriodToHours24(prev.hours, period),
    }));
  }, []);

  const { hours, hours24, minutes, period } = timeParts;
  return useMemo(
    () => ({
      hours,
      hours24,
      minutes,
      period,
      setHours,
      setMinutes,
      setPeriod,
    }),
    [hours, hours24, minutes, period, setHours, setMinutes, setPeriod]
  );
};

// Exports
export type TimePickerProps = {
  id: string;
  name: string;
  label: string;
  date?: Date;
  onSelect?: (nextDate: Date | undefined) => void;
};
export const TimePicker = ({
  date,
  id,
  label,
  name,
  onSelect,
}: TimePickerProps) => {
  const [open, setOpen] = useState<boolean>();
  const { hours, hours24, minutes, period, setHours, setMinutes, setPeriod } =
    useTimeParts(date);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (open) {
        // Scroll selected hour/minute/period items into view
        document
          .querySelectorAll('[data-selected="true"]')
          .forEach((el) => el.scrollIntoView());
      }
    }, 0);
    return () => clearTimeout(timeoutId);
  }, [open]);

  return (
    <FormField id={id} name={name}>
      <FormLabel>{label}</FormLabel>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <FormControl>
            <Button variant="outline" className="h-auto rounded-2xl p-3">
              <AccessTimeOutlined className="h-6 w-6" />
              <span
                className={cn(
                  "grow text-start font-normal",
                  !date && "text-secondary"
                )}
              >
                {date ? format(date, "h:mm aaa") : "Pick a time"}
              </span>
              <CaretSortIcon className="h-6 w-6" />
            </Button>
          </FormControl>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <div className="flex h-60 flex-row px-0.5 pt-1">
            <div className="flex shrink-0 flex-col overflow-y-auto px-0.5 pb-4">
              {[12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11].map((h) => (
                <Button
                  key={h}
                  className="shrink-0 px-5 py-1"
                  data-selected={h === hours}
                  variant={h === hours ? "default" : "ghost"}
                  onClick={() => setHours(h)}
                >
                  {String(h).padStart(2, "0")}
                </Button>
              ))}
            </div>
            <div className="flex shrink-0 flex-col overflow-y-auto px-0.5 pb-4">
              {[0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55].map((m) => (
                <Button
                  key={m}
                  className="shrink-0 px-5 py-1"
                  data-selected={m === minutes}
                  variant={m === minutes ? "default" : "ghost"}
                  onClick={() => setMinutes(m)}
                >
                  {String(m).padStart(2, "0")}
                </Button>
              ))}
            </div>
            <div className="flex shrink-0 flex-col overflow-y-auto px-0.5 pb-4">
              {(["am", "pm"] as const).map((p) => (
                <Button
                  key={p}
                  className="shrink-0 px-5 py-1"
                  data-selected={p === period}
                  variant={p === period ? "default" : "ghost"}
                  onClick={() => setPeriod(p)}
                >
                  {p.toLocaleUpperCase()}
                </Button>
              ))}
            </div>
          </div>
          <Separator />
          <div className="flex justify-end px-3 py-2">
            <Button
              variant="ghost-destructive"
              onClick={() => {
                onSelect?.(undefined);
                setOpen(false);
              }}
            >
              Clear
            </Button>
            <Button
              variant="ghost"
              onClick={() => {
                const nextDate = timePartsToDate({
                  hours,
                  hours24,
                  minutes,
                  period,
                });
                onSelect?.(nextDate);
                setOpen(false);
              }}
            >
              OK
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </FormField>
  );
};
