import { Typography } from "~/@ui/Typography";
import { AccessTimeOutlined } from "@mui/icons-material";
import { ReactNode } from "react";
import { cn } from "~/@shadcn/utils";

// Exports
type Props = {
  children: ReactNode;
  disabled?: boolean;
};
export const TimeStamp = ({ children, disabled = false }: Props) => (
  <span className="inline-flex shrink-0 items-center gap-2">
    <AccessTimeOutlined
      className={cn(
        "ml-[2px] !h-4 !w-4",
        disabled ? "text-secondary" : "text-foreground"
      )}
    />
    <Typography variant="body2" color="secondary" asChild>
      <span>{children}</span>
    </Typography>
  </span>
);
