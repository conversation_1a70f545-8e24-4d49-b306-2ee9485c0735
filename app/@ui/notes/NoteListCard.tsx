import { NavLink, LinkProps, useLocation } from "@remix-run/react";
import {
  CalendarMonthOutlined,
  MicOutlined,
  Videocam,
  Pending,
} from "@mui/icons-material";
import { cn } from "~/@shadcn/utils";
import {
  format,
  formatRelative,
  isToday,
  isYesterday,
  isThisWeek,
} from "date-fns";
import { ProcessingStatus } from "~/api/openapi/generated";
import { AfterHydration } from "~/utils/hydration";

// Helpers
const getNoteIcon = (status: ProcessingStatus, type: string) => {
  if (status === ProcessingStatus.Scheduled) return CalendarMonthOutlined;
  if (status === ProcessingStatus.Uploaded) return Pending;
  if (type === "meeting_recording") return Videocam;
  return MicOutlined;
};

const getFormattedTimestamp = (
  status: ProcessingStatus,
  created: string,
  scheduledStartTime: string | null
) => {
  const now = new Date();

  if (status === ProcessingStatus.Scheduled && scheduledStartTime) {
    const relativeStartTime = formatRelative(new Date(scheduledStartTime), now);
    return `Scheduled for ${relativeStartTime}`;
  }

  const createdDate = new Date(created);

  if (isToday(createdDate)) {
    const createdTime = format(createdDate, "p");
    return `Created today at ${createdTime}`;
  } else if (isYesterday(createdDate)) {
    const createdTime = format(createdDate, "p");
    return `Created yesterday at ${createdTime}`;
  } else if (isThisWeek(createdDate, { weekStartsOn: 1 })) {
    const createdRelative = formatRelative(createdDate, now);
    return `Created ${createdRelative}`;
  } else {
    const createdFullDate = format(createdDate, "MM/dd/yyyy");
    const createdTime = format(createdDate, "p");
    return `Created on ${createdFullDate} ${createdTime}`;
  }
};

// Exports
type Props = LinkProps & {
  status: ProcessingStatus;
  type: string;
  meetingName: string;
  scheduledStartTime: string | null;
  created: string;
};

export const NoteListCard = ({
  status,
  type,
  meetingName,
  scheduledStartTime,
  created,
  to,
  ...linkProps
}: Props) => {
  const location = useLocation();

  // Determine if `to` is an object or a string
  const isActive =
    typeof to === "object" && "pathname" in to
      ? location.pathname === to.pathname
      : location.pathname === to;

  const Icon = getNoteIcon(status, type);

  return (
    <div
      className={cn(
        "flex items-center gap-2 px-4 py-3 text-sm",
        isActive ? "bg-[#FFF8F2]" : "hover:bg-[#F9FAFB]",
        "border-b border-gray-200"
      )}
    >
      <Icon className="h-5 w-5 text-primary" />
      <NavLink
        className="flex grow flex-col overflow-hidden text-ellipsis whitespace-nowrap"
        to={to}
        tabIndex={-1}
        {...linkProps}
      >
        <span className="overflow-hidden text-ellipsis whitespace-nowrap font-medium">
          {meetingName}
        </span>
        <span className="mt-1 text-gray-500">
          <AfterHydration>
            {getFormattedTimestamp(status, created, scheduledStartTime)}
          </AfterHydration>
        </span>
      </NavLink>
    </div>
  );
};
