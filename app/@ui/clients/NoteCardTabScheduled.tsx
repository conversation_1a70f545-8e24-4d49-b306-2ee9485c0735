import { NavLink, LinkProps } from "@remix-run/react";

import { TimeStamp } from "~/@ui/TimeStamp";
import { AfterHydration } from "~/utils/hydration";
import { cn } from "~/@shadcn/utils";
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from "~/@shadcn/ui/card";
import {
  ChevronRightOutlined,
  CalendarMonthOutlined,
} from "@mui/icons-material";

// Exports
type Props = LinkProps & {
  note: any;
  compact?: boolean;
};
const getNoteIcon = () => {
  return CalendarMonthOutlined;
};

export const NoteCardTabScheduled = ({
  note,
  to,
  compact = false,
  ...linkProps
}: Props) => {
  const Icon = getNoteIcon();
  return (
    <Card
      compact={compact}
      className={cn(
        "inline-flex min-w-0 grow flex-row items-center self-stretch hover:border-foreground hover:bg-accent"

        // disabled && "pointer-events-none opacity-50 shadow-none"
      )}
      // ⚠️ The whole Card is an anchor tag, so it must only contain inline
      // elements. This means no paragraphs, and all divs and buttons must be set
      // to display:inline or display:inline-flex.
      asChild
    >
      <NavLink
        // Set tabIndex to -1 to ensure tile is not focusable in disabled state
        tabIndex={-1}
        to={to}
        {...linkProps}
      >
        <CardHeader
          title={note.meetingName}
          className="grow overflow-hidden text-ellipsis whitespace-nowrap"
          asChild
        >
          <span>
            <CardTitle
              className="self-stretch overflow-hidden text-ellipsis whitespace-nowrap"
              variant={compact ? "default" : "h4"}
              asChild
            >
              <span>
                <Icon className="-mt-0.5 mr-1 h-6 w-6 text-primary" />
                {note.meetingName}
              </span>
            </CardTitle>
            {!compact && (
              <CardDescription
                className="inline-flex items-center gap-3 self-stretch"
                asChild
              >
                <span>
                  <TimeStamp>
                    <AfterHydration>{note.created}</AfterHydration>
                  </TimeStamp>
                </span>
              </CardDescription>
            )}
          </span>
        </CardHeader>

        {<ChevronRightOutlined className="h-6 w-6" />}
      </NavLink>
    </Card>
  );
};
