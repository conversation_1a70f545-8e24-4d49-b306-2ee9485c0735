import React from "react";
import { Input } from "~/@shadcn/ui/input";
import { But<PERSON> } from "~/@shadcn/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "~/@shadcn/ui/dialog";
import { ScrollArea } from "~/@shadcn/ui/scroll-area";
import { SearchSharp, ContentCopyOutlined } from "@mui/icons-material";
import { Typography } from "~/@ui/Typography";
import { SummarySection } from "~/api/openapi/generated";

export enum AnswerStatus {
  IDLE = "idle",
  IN_PROCESS = "in process",
  SUCCESS = "success",
  FAILED = "failed",
}

type AskAnythingModalProps = {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  answerStatus: AnswerStatus;
  query: string;
  answer: SummarySection | null;
  handleKeyDown: (e: React.KeyboardEvent<HTMLInputElement>) => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleAsk: () => void;
  handleAddToSummary?: () => void;
  setAnswerStatus: (status: AnswerStatus) => void;
  setQuery: (query: string) => void;
  handleCopy?: () => void;
};

const LoadingIndicator = () => (
  <div className="flex items-center space-x-2">
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500"></div>
    <div className="h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500 delay-200"></div>
    <div className="delay-400 h-1.5 w-1.5 animate-bounce rounded-full bg-gray-500"></div>
  </div>
);

const AskAnythingModal = ({
  isOpen,
  onOpenChange,
  answerStatus,
  query,
  answer,
  handleKeyDown,
  onInputChange,
  handleAsk,
  handleAddToSummary,
  setAnswerStatus,
  setQuery,
  handleCopy,
}: AskAnythingModalProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>
            {answerStatus === AnswerStatus.IDLE
              ? "Ask Anything about this Note"
              : "Ask Anything Results"}
          </DialogTitle>
        </DialogHeader>
        {answerStatus !== AnswerStatus.IDLE && query && (
          <Typography variant="body2">"{query}"</Typography>
        )}
        {answerStatus === AnswerStatus.IDLE ? (
          <div className="flex-row">
            <Input
              className="h-9 rounded-sm"
              value={query}
              onChange={onInputChange}
              onKeyDown={handleKeyDown}
              leftIcon={<SearchSharp />}
            />
            <div className="mt-2 flex justify-end">
              <Button
                className="rounded-sm hover:bg-primary hover:text-white"
                disabled={query === ""}
                onClick={handleAsk}
                size="sm"
              >
                Ask
              </Button>
            </div>
          </div>
        ) : (
          <div>
            {answerStatus === AnswerStatus.IN_PROCESS && (
              <div className="flex items-center space-x-2">
                <LoadingIndicator />
                <Typography variant="body2" className="pr-1">
                  Your answer is on its way...
                </Typography>
              </div>
            )}
            {answerStatus === AnswerStatus.FAILED && (
              <div>
                <Typography variant="body2">
                  I am unable to answer that question.
                </Typography>
                <Typography variant="body2">
                  Try rephrasing your question.
                </Typography>
              </div>
            )}
            {answerStatus === AnswerStatus.SUCCESS && answer && (
              <div>
                <ul>
                  <Typography className="mb-2" variant="h4">
                    {answer.topic}
                  </Typography>
                </ul>
                <ScrollArea className="h-[200px]">
                  <Typography variant="body2">
                    {answer.bullets.map((bullet, index) => (
                      <li
                        key={index}
                        className="mb-5 rounded-md text-foreground"
                      >
                        {bullet}
                      </li>
                    ))}
                  </Typography>
                </ScrollArea>
              </div>
            )}
          </div>
        )}
        <DialogFooter>
          <div className="flex w-full justify-between pt-3">
            {answerStatus === AnswerStatus.SUCCESS && handleAddToSummary && (
              <Button
                onClick={handleAddToSummary}
                variant="outline"
                className="hover:bg-primary hover:text-white"
              >
                <ContentCopyOutlined /> Add to Summary
              </Button>
            )}
            {answerStatus === AnswerStatus.SUCCESS && handleCopy && (
              <Button
                onClick={handleCopy}
                variant="outline"
                className="hover:bg-primary hover:text-white"
              >
                <ContentCopyOutlined /> Copy Formatted Answer
              </Button>
            )}
            {(answerStatus === AnswerStatus.FAILED ||
              answerStatus === AnswerStatus.SUCCESS) && (
              <Button
                onClick={() => {
                  setAnswerStatus(AnswerStatus.IDLE);
                  setQuery("");
                }}
                disabled={answerStatus === AnswerStatus.FAILED && query === ""}
                className="flex items-center gap-2"
              >
                Ask Another Question
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AskAnythingModal;
