import { ReactNode } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, ScrollRestoration } from "@remix-run/react";
import { TooltipProvider } from "@radix-ui/react-tooltip";
import { ClientHintCheck } from "~/utils/hints";

// Exports
type Props = { children: ReactNode };
export const HtmlBody = ({ children }: Props) => (
  <html lang="en">
    <head>
      <meta charSet="utf-8" />
      <meta name="viewport" content="width=device-width,initial-scale=1" />
      <link rel="icon" href="/favicon.ico" />
      <link rel="apple-touch-icon" sizes="128x128" href="/logo192.png" />
      <link rel="manifest" href="/manifest.json" />
      <meta name="theme-color" content="#182349" />
      <Meta />
      <Links />
      <script
        dangerouslySetInnerHTML={{
          // On Firefox only, pages are rendered before some stylesheets are
          // rendered, causing a "flash of unstyled content" (FOUC). Adding a
          // script element with no-op content resolves this.
          // Bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=1404468
          // StackOverflow fix: https://stackoverflow.com/a/64158043/1690132
          __html: "var FIREFOX_FLASH_OF_UNSTYLED_CONTENT_FIX;",
        }}
      />
      <ClientHintCheck />
    </head>
    <body>
      <TooltipProvider delayDuration={100}>{children}</TooltipProvider>
      <ScrollRestoration />
      <Scripts />
    </body>
  </html>
);
