import { LogoutOutlined } from "@mui/icons-material";
import { Link, useLocation } from "@remix-run/react";
import { ReactNode } from "react";
import { Button } from "~/@shadcn/ui/button";
import { CompanyIcon } from "~/@ui/assets/CompanyIcon";
import { CreateButton } from "~/@ui/buttons/CreateButton";
import { useFlag } from "~/context/flags";

type NavConfig = {
  matcher: (path: string) => boolean;
  config: {
    title: string;
    createPath?: string;
    createButtonTooltip?: string;
    showLogoutButton?: boolean;
  };
};

export const TopNavBar = ({ rightChild }: { rightChild?: ReactNode }) => {
  const location = useLocation();
  const isHubEnabled = useFlag("EnableDashboardFeature") ?? false;
  const isInsightsDashboardEnabled =
    useFlag("EnablePracticeInsightsDashboard") ?? false;
  const currentPath = location.pathname;

  const routeConfigs: NavConfig[] = [
    {
      matcher: (path) => isHubEnabled && path.startsWith("/dashboard"),
      config: {
        title: "Advisor Hub",
        createPath: "/notes/create",
        createButtonTooltip: "Create note",
      },
    },
    {
      matcher: (path) =>
        isInsightsDashboardEnabled && path.startsWith("/insights"),
      config: {
        title: "Insights",
      },
    },
    {
      matcher: (path) => path.startsWith("/notes"),
      config: {
        title: "Notes",
        createPath: "/notes/create",
        createButtonTooltip: "Create note",
      },
    },
    {
      matcher: (path) => path.startsWith("/clients"),
      config: {
        title: "Clients",
      },
    },
    {
      matcher: (path) => path.startsWith("/tasks"),
      config: {
        title: "Tasks",
        createPath: "/tasks/create",
        createButtonTooltip: "Create task",
      },
    },
    {
      matcher: (path) => path.startsWith("/settings"),
      config: {
        title: "Settings",
        showLogoutButton: true,
      },
    },
  ];

  const matchedConfig = routeConfigs.find(({ matcher }) =>
    matcher(currentPath)
  )?.config;

  const { title, createPath, createButtonTooltip, showLogoutButton } =
    matchedConfig || {
      title: "Your notes",
      createPath: "/notes/create",
      createButtonTooltip: "Create note",
      showLogoutButton: false,
    };

  return (
    <div className="relative flex w-full justify-between border-b border-gray-200 px-2 md:py-2">
      <div className="flex items-center">
        <Link to="/" className="flex items-center">
          <CompanyIcon className="h-10 w-10 text-blue-600" />
        </Link>
        <h1 className="text-2xl font-semibold text-gray-900">{title}</h1>
      </div>

      <div className="flex items-center">
        {createPath && (
          <div className="hidden md:block">
            <CreateButton
              label="Create"
              to={createPath}
              tooltip={createButtonTooltip}
            />
          </div>
        )}
        {showLogoutButton && (
          <Button
            className="hidden md:flex md:items-center"
            variant="outline"
            asChild
          >
            <Link to="/auth/logout" className="">
              <span>Logout</span>
              <LogoutOutlined />
            </Link>
          </Button>
        )}
        {rightChild}
      </div>
    </div>
  );
};
