import { SearchOutlined } from "@mui/icons-material";
import { FormControl, FormField } from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";

// Exports
type Props = React.ComponentProps<typeof FormField> & {
  inputProps?: React.ComponentProps<typeof Input>;
};
export const SearchField = ({
  name = "searchField",
  inputProps,
  ...props
}: Props) => (
  <FormField name={name} {...props}>
    <FormControl>
      <Input
        size="lg"
        type="search"
        leftIcon={<SearchOutlined />}
        {...inputProps}
      />
    </FormControl>
  </FormField>
);
