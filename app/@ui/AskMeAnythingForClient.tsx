import { useState, useEffect, useCallback } from "react";
import { Input } from "~/@shadcn/ui/input";
import { Button } from "~/@shadcn/ui/button";
import { useFetcher } from "@remix-run/react";
import { Typography } from "~/@ui/Typography";
import { SummarySection } from "~/api/openapi/generated";
import AskAnythingModal, { AnswerStatus } from "~/@ui/AskAnythingModal";
import { SearchSharp } from "@mui/icons-material";

type FetcherData = {
  actionType?: string;
  success?: boolean;
  error?: string;
  answer?: SummarySection;
  searchQueryId?: string;
  sectionIndex?: number;
};

const AskMeAnythingForClient = ({
  clientId,
  query,
  setQuery,
  triggerSearch,
  setTriggerSearch,
}: {
  clientId: string;
  query: string;
  setQuery: (query: string) => void;
  triggerSearch: boolean;
  setTriggerSearch: (triggerSearch: boolean) => void;
}) => {
  const fetcher = useFetcher<FetcherData>();
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [answerStatus, setAnswerStatus] = useState<AnswerStatus>(
    AnswerStatus.IDLE
  );
  const [answer, setAnswer] = useState<SummarySection | null>(null);

  const openModal = () => setIsModalOpen(true);
  const closeModal = () => setIsModalOpen(false);
  const openToggle = (status: boolean) => {
    if (status) {
      setQuery("");
    } else {
      setQuery("");
      closeModal();
    }
  };

  useEffect(() => {
    if (fetcher.state !== "idle" || !fetcher.data) {
      return;
    }
    if (fetcher.data.actionType === "search-client") {
      if (fetcher.data.success && fetcher.data?.answer) {
        setAnswerStatus(AnswerStatus.SUCCESS);
        setAnswer(fetcher.data.answer);
      } else {
        setAnswerStatus(AnswerStatus.FAILED);
      }
    }
  }, [fetcher.data, fetcher.state]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleClick = useCallback(() => {
    setAnswer(null);
    setTriggerSearch(false);
    const formData = new FormData();
    formData.append("actionType", "search-client");
    formData.append("query", query);

    setAnswerStatus(AnswerStatus.IN_PROCESS);
    fetcher.submit(formData, {
      method: "post",
      action: `/clients/${clientId}`,
      encType: "multipart/form-data",
    });
  }, [setTriggerSearch, query, fetcher, clientId]);

  useEffect(() => {
    if (query && triggerSearch) {
      openModal();
      handleClick();
    }
  }, [handleClick, query, triggerSearch]);

  const handleKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === "Enter") {
      event.preventDefault();
      openModal();
      handleClick();
    }
  };

  const handleCopy = () => {
    if (answer) {
      const formatted = `${answer.topic}\n\n${answer.bullets
        .map((b) => `- ${b}`)
        .join("\n")}`;
      navigator.clipboard.writeText(formatted);
    }
  };

  return (
    <>
      <div className="flex w-full items-center space-x-2">
        <Typography variant="body2" className="line-clamp-1 w-full text-right">
          Ask Anything about this Client
        </Typography>
        <div>
          <Input
            className="w-30 h-9 rounded-sm"
            value={query}
            onChange={(e) => {
              setAnswerStatus(AnswerStatus.IDLE);
              setQuery(e.target.value);
            }}
            onKeyDown={handleKeyDown}
            leftIcon={<SearchSharp />}
          />
        </div>
        <Button
          className="rounded-sm hover:bg-primary hover:text-white"
          disabled={query === ""}
          onClick={() => {
            openModal();
            handleClick();
          }}
          size="sm"
          variant="outline"
        >
          Ask
        </Button>
      </div>

      <AskAnythingModal
        isOpen={isModalOpen}
        onOpenChange={openToggle}
        answerStatus={answerStatus}
        query={query}
        answer={answer}
        handleKeyDown={handleKeyDown}
        onInputChange={(e) => {
          setAnswerStatus(AnswerStatus.IDLE);
          setQuery(e.target.value);
        }}
        handleAsk={() => {
          openModal();
          handleClick();
        }}
        setAnswerStatus={setAnswerStatus}
        setQuery={setQuery}
        handleCopy={handleCopy}
      />
    </>
  );
};

export default AskMeAnythingForClient;
