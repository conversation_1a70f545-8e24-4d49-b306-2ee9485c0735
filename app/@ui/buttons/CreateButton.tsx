import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { Link } from "@remix-run/react";
import { ComponentProps } from "react";
import { PlusIcon } from "lucide-react";

// Exports
type Props = {
  label?: string;
  to: ComponentProps<typeof Link>["to"];
  tooltip?: string;
};

export const CreateButton = ({ label, to, tooltip }: Props) => (
  <Tooltip>
    <TooltipTrigger asChild>
      <Button
        size="default"
        className="flex items-center gap-2 rounded-xl bg-[#0F172A] px-4 py-2 text-white hover:bg-[#0E1627] focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
      >
        <Link to={to} className="flex items-center gap-2">
          <PlusIcon />
          {label ?? "Create"}
        </Link>
      </Button>
    </TooltipTrigger>
    <TooltipContent>{tooltip ?? "Create"}</TooltipContent>
  </Tooltip>
);
