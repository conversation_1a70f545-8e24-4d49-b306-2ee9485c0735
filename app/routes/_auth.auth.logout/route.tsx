import type { LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { authenticator } from "~/auth/authenticator.server";

// Exports
export const meta: MetaFunction = () => [
  { title: "Logout" },
  { name: "description", content: "Logout" },
];

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);
  const redirectTo = url.searchParams.get("redirectTo");

  // Log user out and redirect on page load
  return await authenticator.logout(request, {
    redirectTo: redirectTo ?? "/auth/login",
  });
};

const Route = () => {
  // Don't render anything, user should get redirected to /auth/login via loader
  return null;
};
export default Route;
