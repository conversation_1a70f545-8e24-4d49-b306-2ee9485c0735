import React, { useState, useEffect, useRef } from "react";
import {
  AddOutlined,
  FilterListOutlined,
  ArrowDropDown,
  ArrowDropUp,
  Clear,
} from "@mui/icons-material";
import {
  json,
  type LoaderFunctionArgs,
  MetaFunction,
  redirect,
} from "@remix-run/node";
import { SerializeFrom } from "~/types/remix";
import { NavLink, Outlet, useLoaderData, useLocation } from "@remix-run/react";
import { Typography } from "~/@ui/Typography";
import { TaskRow } from "~/@ui/tasks/TaskRow";
import { ContentV2, LayoutV2 } from "~/@ui/layout/LayoutV2";
import { Fab } from "~/@ui/Fab";
import { isBefore, isToday, isThisQuarter, addDays } from "date-fns";
import {
  Configuration,
  ListTasksResponse,
  TaskApi,
  TaskResponse,
} from "~/api/openapi/generated";
import { configurationParameters } from "~/api/openapi/configParams";
import { MultiSelect } from "~/@ui/MultiSelect";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "~/@shadcn/ui/select";
import { logError } from "~/utils/log.server";
import { getUserSessionOrRedirect } from "~/auth/authenticator.server";

const groupTasksByTime = (tasks: ListTasksResponse) => {
  const pastDue: TaskResponse[] = [];
  const dueToday: TaskResponse[] = [];
  const dueThisWeek: TaskResponse[] = [];
  const dueThisQuarter: TaskResponse[] = [];
  const noDueDate: TaskResponse[] = [];

  const now = new Date();
  const endOfWeek = addDays(now, 7);
  tasks.data.forEach((task) => {
    if (!task.dueDate) {
      noDueDate.push(task);
      return;
    }

    const dueDate = new Date(task.dueDate);

    if (isBefore(dueDate, now)) {
      pastDue.push(task);
    } else if (isToday(dueDate)) {
      dueToday.push(task);
    } else if (isBefore(dueDate, endOfWeek)) {
      dueThisWeek.push(task);
    } else if (isThisQuarter(dueDate)) {
      dueThisQuarter.push(task);
    } else {
      dueThisQuarter.push(task);
    }
  });

  return { pastDue, dueToday, dueThisWeek, dueThisQuarter, noDueDate };
};

const groupTasksByAssignee = (tasks: SerializeFrom<TaskResponse>[]) => {
  const grouped: { [key: string]: SerializeFrom<TaskResponse>[] } = {};

  tasks.forEach((task) => {
    const assigneeName = task.assignee?.name || "Unassigned";
    if (!grouped[assigneeName]) {
      grouped[assigneeName] = [];
    }
    grouped[assigneeName]!.push(task);
  });

  return grouped;
};

const groupTasksByOwner = (tasks: SerializeFrom<TaskResponse>[]) => {
  const grouped: { [key: string]: SerializeFrom<TaskResponse>[] } = {};

  tasks.forEach((task) => {
    const OwnerName = task.owner?.name || "Unknown";
    if (!grouped[OwnerName]) {
      grouped[OwnerName] = [];
    }
    grouped[OwnerName]!.push(task);
  });

  return grouped;
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const { userId } = await getUserSessionOrRedirect(request);
  const searchTerm =
    new URL(request.url).searchParams.get("searchTerm") ?? undefined;

  const config = new Configuration(await configurationParameters(request));
  const taskAPI = new TaskApi(config);

  try {
    const tasksResponse = await taskAPI.taskListTasks({ userUuid: userId });
    const validTasks = tasksResponse.data.filter(
      (task): task is TaskResponse => task !== null
    );
    const groupedTasksByTime = groupTasksByTime({ data: validTasks });
    return json({
      searchTerm,
      tasks: { data: validTasks },
      groupedTasksByTime,
    });
  } catch (error: any) {
    if (error.response?.status === 401) {
      throw redirect("/auth/logout");
    }
    logError("Failed to fetch tasks", error);
    return json({
      searchTerm,
      tasks: { data: [] },
      groupedTasksByTime: {
        pastDue: [],
        dueToday: [],
        dueThisWeek: [],
        dueThisQuarter: [],
        noDueDate: [],
      },
    });
  }
};

// Meta Function
export const meta: MetaFunction = () => [
  { title: "Tasks Dashboard" },
  { name: "description", content: "View and manage your tasks" },
];

// Main Component
const Route = () => {
  const { groupedTasksByTime, tasks } = useLoaderData<typeof loader>();
  const location = useLocation();
  const [showFilter, setShowFilter] = useState(false);
  const filterRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Grouping state
  const [groupingOption, setGroupingOption] = useState<
    "time" | "assignee" | "Owner"
  >("time");

  // Local state for filter inputs
  const [createdBy, setCreatedBy] = useState<string[]>([]);
  const [taskStatus, setTaskStatus] = useState<string[]>([]);
  const [assignee, setAssignee] = useState<string[]>([]);

  // State to hold applied filter settings
  const [appliedCreatedBy, setAppliedCreatedBy] = useState<string[]>([]);
  const [appliedTaskStatus, setAppliedTaskStatus] = useState<string[]>([]);
  const [appliedAssignee, setAppliedAssignee] = useState<string[]>([]);
  const [appliedTaskTitle, setAppliedTaskTitle] = useState<string>("");

  // Handle click outside filter modal to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        filterRef.current &&
        !filterRef.current.contains(event.target as Node) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setShowFilter(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filterRef]);

  const uniqueCreatedByOptions = Array.from(
    new Set(tasks.data.map((task) => task?.owner?.name ?? "Unknown"))
  ).map((name) => ({
    label: name,
    value: name,
  }));

  const uniqueAssigneeOptions = Array.from(
    new Set(tasks.data.map((task) => task?.assignee?.name ?? "Unassigned"))
  ).map((name) => ({
    label: name,
    value: name,
  }));

  const handleClearTaskTitle = () => {
    setAppliedTaskTitle("");
  };

  const handleApplyFilters = () => {
    setAppliedCreatedBy(createdBy);
    setAppliedTaskStatus(taskStatus);
    setAppliedAssignee(assignee);
    setShowFilter(false);
  };

  const handleClearFilters = () => {
    setCreatedBy([]);
    setTaskStatus([]);
    setAssignee([]);
    setAppliedCreatedBy([]);
    setAppliedTaskStatus([]);
    setAppliedAssignee([]);
    setAppliedTaskTitle("");
  };

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      handleApplyFilters();
    }
  };

  const toggleFilter = () => {
    setShowFilter(!showFilter);
  };

  const filterTasks = (task: SerializeFrom<TaskResponse>): boolean => {
    if (
      appliedCreatedBy.length > 0 &&
      !appliedCreatedBy.includes(task.owner?.name ?? "")
    ) {
      return false;
    }

    // Map the boolean to string
    const taskStatus = task.completed ? "complete" : "incomplete";

    // Filter by task status
    if (
      appliedTaskStatus.length > 0 &&
      !appliedTaskStatus.includes(taskStatus)
    ) {
      return false;
    }

    // Filter by assignee
    if (
      appliedAssignee.length > 0 &&
      !appliedAssignee.includes(task.assignee?.name ?? "")
    ) {
      return false;
    }

    // Filter by task title
    if (
      appliedTaskTitle &&
      !task.title.toLowerCase().includes(appliedTaskTitle.toLowerCase())
    ) {
      return false;
    }

    return true;
  };

  const renderTaskSection = (
    title: string,
    taskList: SerializeFrom<TaskResponse>[]
  ) => (
    <>
      {taskList.length > 0 ? (
        <div className="flex w-full flex-col gap-3">
          <h3 className="text-lg font-semibold text-gray-700">{title}</h3>
          <div className="overflow-hidden rounded-xl">
            <table className="min-w-full table-auto border-separate border-spacing-0 overflow-hidden rounded-xl border">
              <thead>
                <tr>
                  <th className="w-5 p-3 text-left text-sm font-medium text-gray-600 md:text-base"></th>
                  <th className="min-w-40 max-w-40 p-3 text-left text-sm font-medium text-gray-600 md:min-w-56 md:max-w-56 md:text-base">
                    Task Title
                  </th>
                  <th className="hidden w-32 p-3 text-left text-base font-medium text-gray-600 sm:table-cell md:w-52 md:text-base">
                    Assignee
                  </th>
                  <th className="w-24 shrink-0 p-3 text-left text-sm font-medium text-gray-600 md:text-base">
                    Due Date
                  </th>
                  <th className="w-24 shrink-0 p-3 text-left text-sm font-medium text-gray-600 md:text-base">
                    Status
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {taskList.filter(filterTasks).map((task) => (
                  <TaskRow
                    key={task.uuid}
                    uuid={task.uuid}
                    completed={task.completed}
                    title={task.title}
                    dueAt={
                      task.dueDate
                        ? typeof task.dueDate === "string"
                          ? task.dueDate
                          : task.dueDate.toISOString()
                        : null
                    }
                    selectedAssignee={task.assignee}
                    to={`/tasks/${task.uuid}`}
                  />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      ) : (
        <Typography color="secondary">No tasks</Typography>
      )}
    </>
  );

  const renderGroupedTasks = () => {
    if (groupingOption === "time") {
      return (
        <>
          {groupedTasksByTime.dueToday.length > 0 &&
            renderTaskSection(
              "Today’s Tasks",
              groupedTasksByTime.dueToday as unknown as SerializeFrom<TaskResponse>[]
            )}
          {groupedTasksByTime.dueThisWeek.length > 0 &&
            renderTaskSection(
              "This Week’s Priorities",
              groupedTasksByTime.dueThisWeek as unknown as SerializeFrom<TaskResponse>[]
            )}
          {groupedTasksByTime.dueThisQuarter.length > 0 &&
            renderTaskSection(
              "Upcoming Tasks",
              groupedTasksByTime.dueThisQuarter as unknown as SerializeFrom<TaskResponse>[]
            )}
          {groupedTasksByTime.pastDue.length > 0 &&
            renderTaskSection(
              "Overdue Tasks",
              groupedTasksByTime.pastDue as unknown as SerializeFrom<TaskResponse>[]
            )}
          {groupedTasksByTime.noDueDate.length > 0 &&
            renderTaskSection(
              "Unscheduled Tasks",
              groupedTasksByTime.noDueDate as unknown as SerializeFrom<TaskResponse>[]
            )}
        </>
      );
    }

    const groupedTasks =
      groupingOption === "assignee"
        ? groupTasksByAssignee(
            tasks.data as unknown as SerializeFrom<TaskResponse>[]
          )
        : groupTasksByOwner(
            tasks.data as unknown as SerializeFrom<TaskResponse>[]
          );

    return Object.keys(groupedTasks).map((key) =>
      renderTaskSection(key, groupedTasks[key]!)
    );
  };

  return (
    <LayoutV2>
      <ContentV2
        className="min-w-[60%] lg:max-w-[60%]"
        innerClassName="flex-1"
        floatingAction={
          <Fab asChild>
            <NavLink to="/tasks/create">
              <AddOutlined />
            </NavLink>
          </Fab>
        }
      >
        <div className="relative flex w-full flex-col gap-4 text-gray-500 sm:flex-row sm:items-center sm:justify-between">
          <div className="relative flex w-full items-center">
            <input
              type="text"
              placeholder="Search by task title..."
              value={appliedTaskTitle}
              onChange={(e) => setAppliedTaskTitle(e.target.value)}
              onKeyDown={handleKeyPress}
              className="block w-full rounded border border-gray-300 p-2 shadow-sm"
            />
            {appliedTaskTitle && (
              <button
                onClick={handleClearTaskTitle}
                className="absolute right-2 top-1/2 -translate-y-1/2 transform"
              >
                <Clear fontSize="small" />
              </button>
            )}
          </div>
          <div className="flex justify-end gap-2 ">
            <button
              ref={buttonRef}
              className="flex items-center gap-1 p-2 hover:text-blue-500"
              onClick={toggleFilter}
            >
              <FilterListOutlined fontSize="small" />
              <span>Filter</span>
              {showFilter ? (
                <ArrowDropUp fontSize="small" />
              ) : (
                <ArrowDropDown fontSize="small" />
              )}
            </button>
            {showFilter && (
              <div
                ref={filterRef}
                className="absolute right-0 top-full z-50 mt-2 w-full rounded-md bg-white p-4 shadow-lg md:w-80"
              >
                <div className="flex flex-col space-y-4">
                  <div>
                    <label className="block text-xs font-medium text-gray-500">
                      Created By
                    </label>
                    <MultiSelect
                      options={uniqueCreatedByOptions}
                      selected={createdBy}
                      onChange={setCreatedBy}
                      placeholder="Select created by"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-500">
                      Status
                    </label>
                    <MultiSelect
                      options={[
                        { label: "Complete", value: "complete" },
                        { label: "Incomplete", value: "incomplete" },
                      ]}
                      selected={taskStatus}
                      onChange={setTaskStatus}
                      placeholder="Select status"
                    />
                  </div>

                  <div>
                    <label className="block text-xs font-medium text-gray-500">
                      Assignee
                    </label>
                    <MultiSelect
                      options={uniqueAssigneeOptions}
                      selected={assignee}
                      onChange={setAssignee}
                      placeholder="Select assignee"
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <button
                      className="rounded bg-blue-500 px-4 py-2 text-white"
                      onClick={handleApplyFilters}
                    >
                      Apply Filters
                    </button>
                    <button
                      className="rounded bg-gray-300 px-4 py-2 text-black"
                      onClick={handleClearFilters}
                    >
                      Clear Filters
                    </button>
                  </div>
                </div>
              </div>
            )}
            <div className="relative flex items-center">
              <span className="text-nowrap text-sm font-medium text-gray-500">
                Group By
              </span>
              <Select
                value={groupingOption}
                onValueChange={(value) => setGroupingOption(value as any)}
              >
                <SelectTrigger className="ml-2 h-8 w-28 rounded-md border border-gray-300 text-sm shadow-sm">
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="time">Time</SelectItem>
                  <SelectItem value="assignee">Assignee</SelectItem>
                  <SelectItem value="Owner">Owner</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        {renderGroupedTasks()}
      </ContentV2>

      <Outlet context={{ tasks }} key={location.pathname} />
    </LayoutV2>
  );
};

export default Route;
