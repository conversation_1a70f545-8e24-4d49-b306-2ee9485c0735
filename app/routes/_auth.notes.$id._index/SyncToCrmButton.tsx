import React, { Dispatch, useEffect, useRef, useState } from "react";
import { Button } from "~/@shadcn/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { FormField, FormLabel } from "~/@shadcn/ui/form";
import { OutboxOutlined, SyncOutlined } from "@mui/icons-material";
import { toast } from "react-toastify";
import { useFetcher, useNavigation, useActionData } from "@remix-run/react";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { EditableNoteActions } from "./editableNoteReducer";
import { ApiRoutersCrmClientResponse } from "~/api/openapi/generated";
import LabeledValue from "~/types/LabeledValue";
import CrmClientsDropdown from "./components/CrmClientsDropdown";

type FetcherData = { success?: boolean; error?: string };
type ButtonSize = "icon-lg" | "icon" | "icon-sm" | "default";
type ButtonVariant = "ghost" | "default" | "outline";

export type SyncToCrmCombinedProps = {
  noteId: string;
  disabled: boolean;
  isClientSelectionEnabled?: boolean;
  clients?: ApiRoutersCrmClientResponse[];
  options?: { value: string; label: string }[];
  dispatch?: Dispatch<EditableNoteActions>;
  currentClient?: LabeledValue;
  handleSave: () => void;
  title?: string;
  variant?: ButtonVariant;
  size?: ButtonSize;
};

export const SyncToCrmButton: React.FC<SyncToCrmCombinedProps> = ({
  noteId,
  disabled,
  isClientSelectionEnabled,
  clients,
  options,
  dispatch,
  currentClient,
  handleSave,
  title,
  variant = "ghost",
  size = "icon-sm",
}) => {
  const fetcher = useFetcher<FetcherData>();
  const navigation = useNavigation();
  const actionData = useActionData<{ success?: boolean; errors?: string[] }>(); // from parent
  const toastId = useRef<string | number | null>(null);
  const [certModalOpen, setCertModalOpen] = useState<boolean>(false);
  const [clientModalOpen, setClientModalOpen] = useState<boolean>(false);
  const [certified, setCertified] = useState<boolean>(false);
  const [syncAttempted, setSyncAttempted] = useState(false);
  const [selectedClient, setSelectedClient] = useState<
    LabeledValue | undefined
  >(currentClient); // default to current client; will be updated by user
  const [clientUpdated, setClientUpdated] = useState(false); // flag to trigger save and sync sequence
  const [saveInProgress, setSaveInProgress] = useState(false); // flag to track save progress

  // handle fetcher response for sendToCRM
  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data && syncAttempted) {
      toast.dismiss(toastId.current!);

      if (fetcher.data.success) {
        toast.success("Note synced to CRM", { autoClose: 2000 });
      } else {
        if (
          isClientSelectionEnabled &&
          clients &&
          clients.length > 0 &&
          !clients.some((client) => client.uuid === selectedClient?.value)
        ) {
          setClientModalOpen(true);
        } else {
          toast.error(fetcher.data.error || "Failed to sync to CRM.", {
            autoClose: 2000,
          });
        }
      }
      setSyncAttempted(false);
    }
  }, [
    fetcher.state,
    fetcher.data,
    isClientSelectionEnabled,
    clients,
    selectedClient,
    syncAttempted,
  ]);

  // handle client save completion
  useEffect(() => {
    if (clientUpdated && !saveInProgress) {
      setSaveInProgress(true);
      handleSave();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [clientUpdated, saveInProgress]);

  // trigger sendToCRM
  useEffect(() => {
    // check if save is complete and successful
    if (
      navigation.state === "idle" &&
      saveInProgress &&
      actionData?.success === true // ensure save succeeded
    ) {
      sendToCRM(); // call sendToCRM only after save completes successfully
      setSaveInProgress(false);
      setClientUpdated(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigation.state, saveInProgress, actionData]);

  const sendToCRM = () => {
    setSyncAttempted(true);

    const formData = new FormData();
    formData.append("actionType", "send-to-crm");

    fetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteId}`,
      encType: "multipart/form-data",
    });

    toastId.current = toast.loading("Sending to CRM", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "light",
    });
  };

  const handleButtonClick = () => {
    setCertModalOpen(true);
  };

  const handleCertModalConfirm = () => {
    if (!certified) {
      return;
    }
    setCertModalOpen(false);
    sendToCRM();
  };

  const handleClientModalConfirm = () => {
    if (
      isClientSelectionEnabled &&
      clients &&
      clients.length > 0 &&
      !clients.some((client) => client.uuid === selectedClient?.value)
    ) {
      return;
    }
    setClientModalOpen(false);
    if (dispatch) {
      const clientToUpdate =
        clients?.find(({ uuid }) => uuid === selectedClient?.value) ?? null;
      if (clientToUpdate) {
        dispatch({
          type: "updateClient",
          client: clientToUpdate,
        });
        setClientUpdated(true); // trigger the save and sync sequence
      }
    }
  };

  // when user intentionally closes the "Certification Confirmation" modal (clicks on "Cancel"; presses "Esc")
  const closeRenderCertModal = () => {
    setCertified(false); // uncheck the checkbox
    setCertModalOpen(false); // close the modal
  };

  // when user intentionally closes the "CRM client" modal (clicks on "Cancel"; presses "Esc")
  const closeCrmClientModal = () => {
    setCertified(false); // uncheck the checkbox
    setClientModalOpen(false); // close the modal
    setSelectedClient(currentClient); // reset the selected client
  };

  const onChangeDropdown = (item: LabeledValue | undefined) => {
    setSelectedClient(item);
  };

  const renderCertModal = () => (
    <Dialog open={certModalOpen} onOpenChange={closeRenderCertModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Certification Confirmation
          </DialogTitle>
        </DialogHeader>
        <FormField id="certification" name="certification">
          <label
            className="flex items-center space-x-2 text-sm"
            style={{ pointerEvents: "auto" }}
          >
            <input
              type="checkbox"
              checked={certified}
              onChange={(e) => setCertified(e.target.checked)}
              className="cursor-pointer"
            />
            <span>
              I certify that the interaction record I’m syncing is accurate.
            </span>
          </label>
        </FormField>
        <DialogFooter className="pt-6">
          <Button
            onClick={handleCertModalConfirm}
            disabled={!certified}
            className="bg-green-500 text-white hover:bg-green-600 active:bg-green-700"
          >
            Confirm
          </Button>
          <Button onClick={closeRenderCertModal} variant="ghost">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  const renderClientModal = () => (
    <Dialog open={clientModalOpen} onOpenChange={closeCrmClientModal}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Which client do you want to sync to?
          </DialogTitle>
        </DialogHeader>
        {isClientSelectionEnabled && clients && clients.length > 0 && (
          <FormField id="clientId" name="clientId">
            <FormLabel className="text-sm font-medium text-gray-700">
              CRM Client
            </FormLabel>
            <CrmClientsDropdown
              placeholder="Select a client"
              leftIcon={<OutboxOutlined className="h-5 w-5 text-gray-500" />}
              onChange={onChangeDropdown}
              selectedObject={selectedClient}
              searchOnLabel={true}
              modal
            />
          </FormField>
        )}
        <DialogFooter className="pt-6">
          <Button
            onClick={handleClientModalConfirm}
            className="bg-green-500 text-white hover:bg-green-600 active:bg-green-700"
          >
            Confirm
          </Button>
          <Button onClick={closeCrmClientModal} variant="ghost">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant={variant}
            onClick={handleButtonClick}
            disabled={disabled || navigation.state !== "idle" || saveInProgress}
            data-onboarding="sync-crm-cta"
            aria-label="Sync to CRM"
          >
            <SyncOutlined />{" "}
            {title && <span className="hidden sm:block">{title}</span>}
          </Button>
        </TooltipTrigger>
        {!title && <TooltipContent>Sync to CRM</TooltipContent>}
      </Tooltip>
      {certModalOpen && renderCertModal()}
      {clientModalOpen && renderClientModal()}
    </>
  );
};
