import React, { useState } from "react";
import { <PERSON><PERSON> } from "~/@shadcn/ui/button";
import {
  Di<PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogFooter,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { Typography } from "~/@ui/Typography";
import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectItem,
  SelectValue,
} from "~/@shadcn/ui/select";
import { ArrowRight } from "@mui/icons-material";
import { AttendeeInfo } from "~/api/openapi/generated";
import { SerializeFrom } from "~/types/remix";

// Modal for Remapping Speakers
type RemapSpeakersModalProps = {
  isOpen: boolean;
  onClose: () => void;
  speakers: string[];
  attendees: SerializeFrom<AttendeeInfo>[];
  onConfirm: (mapping: { [key: string]: string }) => void;
};

export const RemapSpeakersModal: React.FC<RemapSpeakersModalProps> = ({
  isOpen,
  onClose,
  speakers,
  attendees,
  onConfirm,
}) => {
  const [mapping, setMapping] = useState<{ [key: string]: string }>({});

  const handleSelectChange = (speaker: string, attendeeId: string) => {
    setMapping((prev) => ({
      ...prev,
      [speaker]: attendeeId,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">
            Remap Speakers
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {speakers.map((speaker, index) => (
            <div
              key={`${speaker}-${index}`}
              className="flex items-center gap-4"
            >
              <Typography className="shrink-0 font-medium text-gray-700">
                {speaker}
              </Typography>

              <ArrowRight className="text-gray-500" />

              <div className="w-full max-w-xs">
                <Select
                  value={mapping[speaker] || ""}
                  onValueChange={(value) => handleSelectChange(speaker, value)}
                >
                  <SelectTrigger className="w-full rounded-md border-gray-300 text-sm shadow-sm">
                    <SelectValue placeholder="Select Attendee" />
                  </SelectTrigger>
                  <SelectContent>
                    {attendees.map((attendee) => (
                      <SelectItem key={attendee.uuid} value={attendee.uuid}>
                        {attendee.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          ))}
        </div>

        <DialogFooter className="pt-6">
          <Button onClick={() => onConfirm(mapping)} variant="success">
            Confirm
          </Button>
          <Button onClick={onClose} variant="ghost">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
