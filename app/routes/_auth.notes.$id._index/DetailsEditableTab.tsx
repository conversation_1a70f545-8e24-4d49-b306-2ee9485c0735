import { useState, useEffect, Dispatch } from "react";
import ReactMarkdown from "react-markdown";

import { Checkbox } from "~/@shadcn/ui/checkbox";
import { TextareaGrowable } from "~/@shadcn/ui/textarea";
import { Typography } from "~/@ui/Typography";
import {
  EditableNoteActions,
  EditableNoteState,
} from "~/routes/_auth.notes.$id._index/editableNoteReducer";
import { copyToClipboard } from "~/utils/copyToClipboard";
import shouldShowCopyButton from "~/routes/_auth.notes.$id._index/utils/shouldShowCopyButton";
import { ArrowUpRightIcon, CopyIcon, PlusIcon } from "lucide-react";

// Fragments
type EditableActionItemsProps = {
  dispatch: Dispatch<EditableNoteActions>;
  actionItems: EditableNoteState["actionItems"];
  disabled: boolean;
  onEdit: () => void;
  isVisible: boolean;
};

const EditableActionItems = ({
  dispatch,
  actionItems,
  disabled,
  onEdit,
  isVisible,
}: EditableActionItemsProps) => {
  const [itemsState, setItemsState] = useState(
    actionItems.map((item) => ({
      ...item,
      value: item.value,
    }))
  );

  useEffect(() => {
    setItemsState(
      actionItems.map((item) => ({
        ...item,
        value: item.value,
      }))
    );
  }, [actionItems]);

  const handleAddItem = () => {
    if (disabled) return;
    dispatch({ type: "addActionItem" });
  };

  const handleCopySection = () => {
    const text = [
      "Action items:",
      ...itemsState.map((item) => item.value),
    ].join("\n   • ");
    copyToClipboard(text, "Action items");
  };

  const handleItemChange = (uuid: string, value: string) => {
    const updatedItems = itemsState.map((item) =>
      item.uuid === uuid ? { ...item, value } : item
    );

    setItemsState(updatedItems);

    dispatch({
      type: "updateActionItem",
      uuid: uuid,
      nextValue: value,
    });

    onEdit();
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      handleAddItem();
    }
  };

  return (
    <>
      <div className="flex items-center gap-1">
        <Typography variant="h3" color="primary">
          Action items
        </Typography>
        {shouldShowCopyButton(actionItems) && (
          <CopyIcon
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleCopySection}
            data-onboarding="copy-icon"
          />
        )}
        {!disabled && (
          <PlusIcon
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleAddItem}
          />
        )}
      </div>
      <ul className="flex list-disc flex-col gap-2 pl-4">
        {itemsState
          .filter(({ action }) => action !== "delete")
          .map(({ uuid, value, autoFocus, checked }, index) => (
            <li
              key={uuid}
              className="-ml-2 flex items-center gap-1 whitespace-pre-wrap text-warning"
            >
              <Checkbox
                defaultChecked={checked}
                onCheckedChange={(checked) => {
                  if (!disabled && typeof checked === "boolean") {
                    dispatch({ type: "setActionItemChecked", uuid, checked });
                    onEdit();
                  }
                }}
                disabled={disabled}
              />
              {disabled ? (
                <Typography
                  className="flex-grow overflow-hidden break-words text-lg"
                  asChild
                >
                  <ReactMarkdown>{value}</ReactMarkdown>
                </Typography>
              ) : (
                <TextareaGrowable
                  autoFocus={autoFocus}
                  value={value}
                  placeholder="Add an action item"
                  className="flex-grow border-none text-lg !opacity-100 focus:outline-none"
                  onChange={(event) => {
                    if (disabled) {
                      return;
                    }
                    handleItemChange(uuid, event.currentTarget.value);
                  }}
                  onBlur={(event) => {
                    if (
                      !disabled &&
                      event.currentTarget.value.trim().length === 0
                    ) {
                      dispatch({ type: "removeActionItem", uuid });
                      onEdit();
                    }
                  }}
                  onKeyDown={handleKeyDown}
                  disabled={disabled}
                  isVisible={isVisible}
                />
              )}
              <ArrowUpRightIcon
                className="ml-2 cursor-pointer text-gray-500 hover:text-black"
                onClick={() => (window.location.href = `/tasks/${uuid}`)}
              />
            </li>
          ))}
      </ul>
    </>
  );
};

type EditableKeyTakeawaysProps = {
  dispatch: Dispatch<EditableNoteActions>;
  keyTakeaways: EditableNoteState["keyTakeaways"];
  disabled: boolean;
  onEdit: () => void;
  isVisible: boolean;
};

const EditableKeyTakeaways = ({
  dispatch,
  keyTakeaways,
  disabled,
  onEdit,
  isVisible,
}: EditableKeyTakeawaysProps) => {
  const [takeawaysState, setTakeawaysState] = useState(
    keyTakeaways.map((takeaway) => ({
      ...takeaway,
      value: takeaway.value,
    }))
  );

  useEffect(() => {
    setTakeawaysState(
      keyTakeaways.map((takeaway) => ({
        ...takeaway,
        value: takeaway.value,
      }))
    );
  }, [keyTakeaways]);

  const handleAddTakeaway = () => {
    if (disabled) return;
    dispatch({ type: "addKeyTakeaway" });
  };

  const handleCopySection = () => {
    const text = [
      "Key takeaways:",
      ...takeawaysState.map((item) => item.value),
    ].join("\n   • ");
    copyToClipboard(text, "Key takeaways");
  };

  const handleTakeawayChange = (index: number, value: string) => {
    const updatedTakeaways = [...takeawaysState];
    updatedTakeaways[index]!.value = value;
    setTakeawaysState(updatedTakeaways);
    dispatch({
      type: "updateKeyTakeaway",
      uuid: updatedTakeaways[index]!.uuid,
      nextValue: value,
    });
    onEdit();
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      handleAddTakeaway();
    }
  };

  return (
    <>
      <div className="flex items-center gap-1">
        <Typography variant="h3" color="primary">
          Key takeaways
        </Typography>
        {shouldShowCopyButton(keyTakeaways) && (
          <CopyIcon
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleCopySection}
          />
        )}
        {!disabled && (
          <PlusIcon
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleAddTakeaway}
          />
        )}
      </div>
      <ul className="flex list-disc flex-col gap-2 pl-4">
        {takeawaysState.map(({ uuid, value, autoFocus }, index) => (
          <li key={uuid} className="whitespace-pre-wrap text-warning">
            {disabled ? (
              <Typography
                className="flex-grow overflow-hidden break-words text-lg"
                asChild
              >
                <ReactMarkdown>{value}</ReactMarkdown>
              </Typography>
            ) : (
              <TextareaGrowable
                autoFocus={autoFocus}
                value={value}
                placeholder="Add a key takeaway"
                className="border-none text-lg !opacity-100 focus:outline-none"
                onChange={(event) => {
                  if (disabled) {
                    return;
                  }
                  handleTakeawayChange(index, event.currentTarget.value);
                }}
                onBlur={(event) => {
                  if (
                    !disabled &&
                    event.currentTarget.value.trim().length === 0
                  ) {
                    dispatch({ type: "removeKeyTakeaway", uuid });
                    onEdit();
                  }
                }}
                onKeyDown={handleKeyDown}
                disabled={disabled}
                isVisible={isVisible}
              />
            )}
          </li>
        ))}
      </ul>
    </>
  );
};

type EditableAdvisorNotesProps = {
  dispatch: Dispatch<EditableNoteActions>;
  advisorNotes: EditableNoteState["advisorNotes"];
  disabled: boolean;
  onEdit: () => void;
  isVisible: boolean;
};

const EditableAdvisorNotes = ({
  dispatch,
  advisorNotes,
  disabled,
  onEdit,
  isVisible,
}: EditableAdvisorNotesProps) => {
  const [notesState, setNotesState] = useState(
    advisorNotes.map((note) => ({
      ...note,
      value: note.value,
    }))
  );

  useEffect(() => {
    setNotesState(
      advisorNotes.map((note) => ({
        ...note,
        value: note.value,
      }))
    );
  }, [advisorNotes]);

  const handleAddNote = () => {
    if (disabled) return;
    dispatch({ type: "addAdvisorNote" });
  };

  const handleCopySection = () => {
    const text = [
      "Advisor notes:",
      ...notesState.map((item) => item.value),
    ].join("\n   • ");
    copyToClipboard(text, "Advisor notes");
  };

  const handleNoteChange = (index: number, value: string) => {
    const updatedNotes = [...notesState];
    updatedNotes[index]!.value = value;
    setNotesState(updatedNotes);
    dispatch({
      type: "updateAdvisorNote",
      uuid: updatedNotes[index]!.uuid,
      nextValue: value,
    });
    onEdit();
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === "Enter" && !disabled) {
      event.preventDefault();
      handleAddNote();
    }
  };

  return (
    <>
      <div className="flex items-center gap-1">
        <Typography variant="h3" color="primary">
          Advisor notes
        </Typography>
        {shouldShowCopyButton(advisorNotes) && (
          <CopyIcon
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleCopySection}
          />
        )}
        {!disabled && (
          <PlusIcon
            className="ml-2 cursor-pointer text-gray-500 hover:text-black"
            onClick={handleAddNote}
          />
        )}
      </div>
      <ul className="flex list-disc flex-col gap-2 pl-4">
        {notesState.map(({ uuid, value, autoFocus }, index) => (
          <li key={uuid} className="whitespace-pre-wrap text-warning">
            {disabled ? (
              <Typography
                className="flex-grow overflow-hidden break-words text-lg"
                asChild
              >
                <ReactMarkdown>{value}</ReactMarkdown>
              </Typography>
            ) : (
              <TextareaGrowable
                autoFocus={autoFocus}
                value={value}
                placeholder="Add an advisor note"
                className="border-none text-lg !opacity-100 focus:outline-none"
                onChange={(event) => {
                  if (disabled) {
                    return;
                  }
                  handleNoteChange(index, event.currentTarget.value);
                }}
                onBlur={(event) => {
                  if (
                    !disabled &&
                    event.currentTarget.value.trim().length === 0
                  ) {
                    dispatch({ type: "removeAdvisorNote", uuid });
                    onEdit();
                  }
                }}
                onKeyDown={handleKeyDown}
                disabled={disabled}
                isVisible={isVisible}
              />
            )}
          </li>
        ))}
      </ul>
    </>
  );
};

// Exports
type DetailsEditableTabProps = {
  dispatch: Dispatch<EditableNoteActions>;
  actionItems: EditableNoteState["actionItems"];
  advisorNotes: EditableNoteState["advisorNotes"];
  keyTakeaways: EditableNoteState["keyTakeaways"];
  disabled: boolean;
  onEdit: () => void;
  isEditMode: boolean;
  enableEditMode: () => void;
  isVisible: boolean;
};

export const DetailsEditableTab = ({
  dispatch,
  actionItems,
  advisorNotes,
  keyTakeaways,
  disabled,
  onEdit,
  isEditMode,
  enableEditMode,
  isVisible,
}: DetailsEditableTabProps) => {
  // if the note is empty and not in edit mode, show a message
  if (
    !isEditMode &&
    !actionItems.length &&
    !keyTakeaways.length &&
    !advisorNotes.length
  ) {
    return (
      <div className="text-gray-600">
        This was an empty note, but you can{" "}
        <span
          onClick={enableEditMode}
          className="cursor-pointer text-blue-500 underline"
        >
          edit it
        </span>{" "}
        to add some details.
      </div>
    );
  }

  return (
    <>
      <EditableActionItems
        dispatch={dispatch}
        actionItems={actionItems}
        disabled={disabled}
        onEdit={onEdit}
        isVisible={isVisible}
      />
      <EditableKeyTakeaways
        dispatch={dispatch}
        keyTakeaways={keyTakeaways}
        disabled={disabled}
        onEdit={onEdit}
        isVisible={isVisible}
      />
      <EditableAdvisorNotes
        dispatch={dispatch}
        advisorNotes={advisorNotes}
        disabled={disabled}
        onEdit={onEdit}
        isVisible={isVisible}
      />
    </>
  );
};

export default DetailsEditableTab;
