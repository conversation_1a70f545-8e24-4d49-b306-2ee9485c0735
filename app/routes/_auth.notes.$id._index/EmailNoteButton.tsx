import { Mail } from "lucide-react";

import { Button } from "~/@shadcn/ui/button";
import { toast } from "react-toastify";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import { useFetcher } from "@remix-run/react";
import { useEffect, useRef } from "react";

type FetcherData = { success?: boolean; error?: string };

// Exports
export const EmailNoteButton = ({
  noteId,
  className,
}: {
  noteId: string;
  className?: string;
}) => {
  const fetcher = useFetcher<FetcherData>();
  const toastId = useRef<string | number | null>(null);

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (fetcher.data.success) {
        toast.update(toastId.current!, {
          render: "Email sent successfully",
          type: toast.TYPE.SUCCESS,
          isLoading: false,
          autoClose: 2000,
        });
      } else {
        toast.update(toastId.current!, {
          render: fetcher.data.error || "Failed to send email",
          type: toast.TYPE.ERROR,
          isLoading: false,
          autoClose: 2000,
        });
      }
    }
  }, [fetcher.state, fetcher.data]);

  const handleClick = () => {
    const formData = new FormData();
    formData.append("actionType", "email-note");

    fetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteId}`,
      encType: "multipart/form-data",
    });

    toastId.current = toast.loading("Sending email", {
      position: "top-center",
      autoClose: 2000,
      hideProgressBar: true,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      progress: undefined,
      theme: "light",
    });
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          onClick={handleClick}
          className={className}
          data-onboarding="send-email-cta"
        >
          <Mail /> <span className="hidden sm:block">Email Notes</span>
        </Button>
      </TooltipTrigger>
      <TooltipContent>Send Email</TooltipContent>
    </Tooltip>
  );
};
