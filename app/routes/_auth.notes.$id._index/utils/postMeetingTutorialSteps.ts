type PostMeetingTutorialOptions = {
  enableNoteSearch: boolean;
  enableShareNote: boolean;
};

const postMeetingTutorialSteps = ({
  enableNoteSearch,
  enableShareNote,
}: PostMeetingTutorialOptions) => [
  {
    title: "Post Meeting",
    intro: "The Note page updates with all of the details from your meeting.",
  },
  {
    title: "Email",
    element: "[data-onboarding='send-email-cta']",
    intro:
      "Clicking the email button will email the Details and Summary to your email address.",
  },
  {
    title: "Follow-up Email",
    element: "[data-onboarding='preview-email-cta']",
    intro:
      "Automatically generate a follow-up email that you can copy and paste into your email program.",
  },
  {
    title: "Sync or Finalize",
    element: "[data-onboarding='sync-crm-cta']",
    intro:
      "Sync notes directly to your client’s account in your CRM. If you don’t have a CRM you can finalize the note and lock it from further editing.",
  },
  ...(enableShareNote
    ? [
        {
          title: "Share Your Note",
          element: "[data-testid='ShareRoundedIcon']",
          intro: "You can share your note with anyone in your organization.",
        },
      ]
    : []),
  {
    title: "Edit",
    element: "[data-onboarding='edit-note-icon']",
    intro:
      "Click the edit button to edit your note. This changes to a Save button once in Edit mode.",
  },
  ...(enableNoteSearch
    ? [
        {
          title: "Ask Anything",
          element: "[data-onboarding='ask-anything']",
          intro:
            "Ask anything about your note. From here, you can add its response to the Summary or copy it.",
        },
      ]
    : []),
  {
    title: "Keywords",
    element: "[data-onboarding='notes-keywords']",
    intro:
      "Zeplyn will analyze your meeting and identify specific keywords that can be used for filtering.",
  },
  {
    title: "Meeting Note Navigation",
    element: "[data-onboarding='notes-navigation']",
    intro:
      "After the meeting, the tabs will update to include related information. For client meetings that may include a Client Review, Life Events, and Numerical Data.",
  },
  {
    title: "Copy",
    element: "[data-onboarding='copy-icon']",
    intro:
      "Any time you see this icon you can copy that entire section. Clicking the arrows next to the tasks will take you to the tasks page.",
  },
  {
    title: "Summary",
    element: "[aria-label='Show summary']",
    intro:
      "Let’s take a look at the Summary tab where you’ll get all the details of your meeting. Click the Summary tab.",
  },
];

export default postMeetingTutorialSteps;
