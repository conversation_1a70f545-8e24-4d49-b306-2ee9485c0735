import { useState, useEffect } from "react";
import { Link, useFetcher } from "@remix-run/react";
import { marked } from "marked";
import { Copy, MailPlus } from "lucide-react";
import { toast } from "react-toastify";

import { Button } from "~/@shadcn/ui/button";
import { Tooltip, TooltipContent, TooltipTrigger } from "~/@shadcn/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
} from "~/@shadcn/ui/dialog";
import { Typography } from "~/@ui/Typography";

import { useFlag } from "~/context/flags";
import { MeetingSummaryEmailTemplate } from "~/api/openapi/generated";
import { Spinner } from "~/@ui/assets/Spinner";
import stripHtml from "~/utils/stripHtml";
import MarkdownEditor from "~/@ui/MarkdownEditor";

type FetcherData = {
  success?: boolean;
  error?: string;
  mailtoLink?: string;
  templates?: MeetingSummaryEmailTemplate[];
};

const toastId = "FollowUpEmailButton-generate";

export const FollowUpEmailButton = ({
  noteId,
  fetchOnMount = true,
}: {
  noteId: string;
  fetchOnMount?: boolean;
}) => {
  const fetcher = useFetcher<FetcherData>();
  const [isModalOpen, setModalOpen] = useState(false);
  const [emailPreview, setEmailPreview] = useState({
    subject: "",
    body: "",
    content_type: "plain",
    mailtoLink: "",
  });

  const templatesEnabled = useFlag("EnableMeetingSummaryEmailTemplates");
  const [templates, setTemplates] = useState<MeetingSummaryEmailTemplate[]>([]);
  const isChangingTemplate = fetcher.state !== "idle";

  const fetchTemplatesIfNeeded = () => {
    if (!templatesEnabled || templates.length > 0) {
      return false;
    }
    const formData = new FormData();
    formData.append("actionType", "get-email-templates");
    fetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteId}`,
      encType: "multipart/form-data",
    });
    return true;
  };

  // If the templates need to be fetched on mount, fetch them.
  useEffect(() => {
    if (fetchOnMount) {
      fetchTemplatesIfNeeded();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleEmailGeneration = (templateId: string = "default") => {
    const formData = new FormData();
    formData.append("actionType", "send-follow-up-email");

    if (templatesEnabled && templateId !== "default") {
      formData.append("templateId", templateId);
    }
    fetcher.submit(formData, {
      method: "post",
      action: `/notes/${noteId}`,
      encType: "multipart/form-data",
    });

    if (!isModalOpen) {
      toast.loading("Preparing follow up", {
        position: "top-center",
        autoClose: false,
        toastId,
      });
    }
  };

  useEffect(() => {
    if (fetcher.state === "idle" && fetcher.data) {
      if (templatesEnabled && fetcher.data?.templates) {
        const recs = fetcher.data.templates;
        const defaultTemplate = recs.find(
          (t) => t.name === "Default Email Template"
        );
        const otherTemplates = recs
          .filter((t) => t.name !== "Default Email Template")
          .sort((a, b) => a.name.localeCompare(b.name));
        const sortedTemplates = defaultTemplate
          ? [defaultTemplate, ...otherTemplates]
          : otherTemplates;
        setTemplates(sortedTemplates);
      }

      if (fetcher.data.success && fetcher.data?.mailtoLink) {
        const emailData = parseMailto(fetcher.data.mailtoLink);
        setEmailPreview({ ...emailData, mailtoLink: fetcher.data.mailtoLink });
        setModalOpen(true);
        if (!isChangingTemplate) {
          toast.update(toastId, {
            render: "Draft ready. You can preview it.",
            type: toast.TYPE.SUCCESS,
            isLoading: false,
            autoClose: 2000,
          });
        }
      } else if (fetcher.data.error) {
        toast.update(toastId, {
          render: fetcher.data.error || "Failed to create draft",
          type: toast.TYPE.ERROR,
          isLoading: false,
          autoClose: 2000,
        });
      }
    }
  }, [fetcher.state, fetcher.data, templatesEnabled, isChangingTemplate]);

  // After the templates have been fetched, if we didn't fetch templates when mounting the
  // component, start the email generation flow. The limited list of dependencies is
  // intentional: we want this to run only if fetchOnMount is false and if the list of
  // templates has changed, which we use as the signal to indicate that the templates have
  // just been fetched as part of the email generation flow.
  useEffect(() => {
    if (fetchOnMount) return;
    if (templates.length === 0) return;
    handleEmailGeneration("default");
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fetchOnMount, templates]);

  const parseMailto = (mailtoLink: string) => {
    const url = new URL(mailtoLink);
    const params = new URLSearchParams(url.search);
    const subject = params.get("subject") || "";
    const body = params.get("body") || "";
    const content_type =
      params.get("content_type") === "html" ? "html" : "plain";

    return { subject, body, content_type };
  };

  const handleCopy = async () => {
    const htmlContent = await marked.parse(emailPreview.body);

    const clipboardItemData = {
      "text/plain": stripHtml(htmlContent),
      "text/html": htmlContent,
    };

    const clipboardItem = new ClipboardItem(clipboardItemData);
    await navigator.clipboard.write([clipboardItem]);

    toast.success("Email content copied to clipboard!", {
      autoClose: 2000,
      isLoading: false,
    });
  };

  const mailtoLinkTooLarge = !!(
    emailPreview.mailtoLink && emailPreview.mailtoLink.length >= 2000
  );

  const closeModal = () => {
    setModalOpen(false);
  };

  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            onClick={() => {
              if (fetchTemplatesIfNeeded()) {
                toast.loading("Preparing follow up", {
                  position: "top-center",
                  toastId,
                });
                return;
              }
              handleEmailGeneration("default");
            }}
            data-onboarding="preview-email-cta"
          >
            <MailPlus />{" "}
            <span className="hidden sm:block">Preview Follow-up</span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Preview Follow-up Email</TooltipContent>
      </Tooltip>

      <Dialog open={isModalOpen} onOpenChange={closeModal}>
        <DialogContent className="flex h-auto max-h-[95vh] w-full max-w-5xl flex-col">
          <DialogHeader>
            <div className="flex w-full items-center justify-between pr-10">
              <DialogTitle>Email Preview</DialogTitle>
            </div>
          </DialogHeader>

          <div className="flex min-h-0 flex-1 flex-col space-y-2 p-4">
            <Typography variant="body2">
              <strong>Subject:</strong> {emailPreview.subject}
            </Typography>

            <Typography
              variant="body2"
              className="flex w-full items-center justify-between gap-2"
            >
              <strong>Message:</strong>
              {templatesEnabled && templates.length >= 1 && (
                <select
                  id="template-select"
                  onChange={(e) => handleEmailGeneration(e.target.value)}
                  defaultValue="default"
                  disabled={isChangingTemplate}
                  className="ml-4 rounded border p-1"
                >
                  {templates.map((template) => (
                    <option key={template.uuid} value={template.uuid}>
                      {template.name === "Default Email Template"
                        ? `⭐ ${template.name}`
                        : template.name}
                    </option>
                  ))}
                </select>
              )}
            </Typography>

            {isChangingTemplate ? (
              <div className="flex h-48 items-center justify-center rounded-md border bg-white bg-opacity-75 p-2">
                <Spinner className="h-10 w-10" />
              </div>
            ) : (
              <MarkdownEditor markdown={emailPreview.body} readOnly />
            )}
          </div>

          <DialogFooter className="shrink-0 gap-y-2">
            <Button
              onClick={handleCopy}
              disabled={isChangingTemplate}
              className="flex items-center gap-2"
            >
              <Copy /> Copy Email Content
            </Button>
            {emailPreview.mailtoLink && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button disabled={mailtoLinkTooLarge} asChild>
                    <Link to={emailPreview.mailtoLink}>Create draft email</Link>
                  </Button>
                </TooltipTrigger>
                {mailtoLinkTooLarge && (
                  <TooltipContent>
                    Contents too large to send to email client
                  </TooltipContent>
                )}
              </Tooltip>
            )}
            <Button onClick={closeModal} variant="ghost">
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};
