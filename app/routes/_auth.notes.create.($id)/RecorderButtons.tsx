import React from "react";
import {
  PauseOutlined,
  MicOutlined,
  RestartAltOutlined,
} from "@mui/icons-material";
import { Typography } from "~/@ui/Typography";
import { cn } from "~/@shadcn/utils";
import { Button, ButtonProps } from "~/@shadcn/ui/button";
import { useSubmit } from "@remix-run/react";

// Types
export type Recording = { recordingBlob: Blob; recordingUrl: string };

// Fragments
const Pulser = ({ active = false }: { active?: boolean }) => (
  <span className="relative mr-2 inline-flex h-3 w-3">
    {active && (
      <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-destructive opacity-75" />
    )}
    <span
      className={cn(
        "relative inline-flex h-3 w-3 rounded-full",
        active ? "bg-destructive" : "bg-secondary"
      )}
    />
  </span>
);

const RecordingTime = ({
  isRecording = false,
  isPaused = false,
  seconds,
}: {
  isRecording?: boolean;
  isPaused?: boolean;
  seconds: number;
}) => {
  const minutesString = String(Math.floor(seconds / 60)).padStart(2, "0");
  const secondsString = String(seconds % 60).padStart(2, "0");
  return (
    <React.Fragment>
      <Typography>
        <Pulser active={isRecording && !isPaused} />
        <span>
          {minutesString}:{secondsString}
        </span>
      </Typography>
      <Typography color="secondary" variant="body2">
        <span>
          {!isRecording && "Not started"}
          {isRecording && !isPaused && "In progress"}
          {isRecording && isPaused && "Paused"}
        </span>
      </Typography>
    </React.Fragment>
  );
};
const RecordButton = ({ label, ...props }: ButtonProps & { label: string }) => (
  <Button className="w-28" variant="destructive" {...props}>
    <MicOutlined />
    {label}
  </Button>
);
const PauseButton = (props: ButtonProps) => (
  <Button className="w-28" variant="destructive" {...props}>
    <PauseOutlined />
    Pause
  </Button>
);
const ResetButton = (props: ButtonProps) => (
  <Button variant="ghost-destructive" {...props}>
    Reset
    <RestartAltOutlined />
  </Button>
);

export const RecorderCardButtons = ({
  isPaused,
  isRecording,
  recordingTime,
  showResetButton,
  useSubmitForm,
  startRecording,
  resumeRecording,
  pauseRecording,
  resetRecorder,
}: {
  isPaused: boolean;
  isRecording: boolean;
  showResetButton: boolean;
  useSubmitForm: boolean;
  recordingTime: number;
  startRecording: () => void;
  resumeRecording: () => void;
  pauseRecording: () => void;
  resetRecorder: () => void;
}) => {
  const submit = useSubmit();
  return (
    <div>
      <div className="flex select-none flex-row items-center gap-3 text-card-foreground">
        {(() => {
          // Initial state
          if (!isRecording) {
            return (
              <RecordButton
                type="submit"
                label="Start"
                name="intent"
                value="start_recording"
                onClick={(e) => {
                  startRecording();
                  if (useSubmitForm) {
                    submit(e.currentTarget);
                  }
                }}
              />
            );
          }

          // Active state
          if (isPaused) {
            return (
              <RecordButton
                type="submit"
                name="intent"
                value="resume_recording"
                label="Resume"
                onClick={(e) => {
                  resumeRecording();
                  if (useSubmitForm) {
                    submit(e.currentTarget);
                  }
                }}
              />
            );
          }

          return (
            <PauseButton
              type="submit"
              name="intent"
              value="pause_recording"
              onClick={(e) => {
                pauseRecording();
                if (useSubmitForm) {
                  submit(e.currentTarget);
                }
              }}
            />
          );
        })()}
        <div className="flex w-full grow flex-col">
          <RecordingTime
            isRecording={isRecording}
            isPaused={isPaused}
            seconds={recordingTime}
          />
        </div>
        <div className="flex shrink-0">
          {showResetButton && isRecording && (
            <ResetButton onClick={() => resetRecorder()} />
          )}
        </div>
      </div>
    </div>
  );
};
