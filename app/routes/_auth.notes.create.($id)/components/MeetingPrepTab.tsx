import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { CalendarRange, Notebook<PERSON>en, Pen } from "lucide-react";
import { ContentCopy, SaveOutlined } from "@mui/icons-material";
import debounce from "lodash.debounce";

import { But<PERSON> } from "~/@shadcn/ui/button";
import { Typography } from "~/@ui/Typography";
import { ClientInteraction, FollowUpStatus } from "~/api/openapi/generated";
import { followUpDataForFollowUp } from "~/routes/_auth.notes.$id._index/followUpDataTypes";
import { GenerateAgendaEmailButton } from "~/routes/_auth.dashboard/components/buttons";
import { AttendeesList } from "~/routes/_auth.dashboard/components/AttendeesList";
import { AttendeeOption } from "~/api/attendees/types";
import { Divider } from "~/@ui/Divider";
import { Skeleton } from "~/@shadcn/ui/skeleton";
import { useFetcher } from "@remix-run/react";
import { Spinner } from "~/@ui/assets/Spinner";
import MarkdownEditor from "~/@ui/MarkdownEditor";
import AskMeAnythingForClient from "~/@ui/AskMeAnythingForClient";

export const SkeletonMeetingPrepTab = () => (
  <>
    <Skeleton className="h-20 w-full" />
    <Divider />
    <div className="flex h-80 gap-2">
      <Skeleton className="h-full w-1/2" />
      <Skeleton className="h-full w-1/2" />
    </div>
  </>
);

const MeetingPrepTab = ({
  interaction,
  clients,
  readOnly = false,
}: {
  interaction?: ClientInteraction;
  clients: AttendeeOption[];
  readOnly: boolean;
}) => {
  const [agendaEditable, setAgendaEditable] = useState(false);
  const [advisorNotesEditable, setAdvisorNotesEditable] = useState(false);

  const agendaFetcher = useFetcher();
  const [query, setQuery] = useState("");
  const [triggerSearch, setTriggerSearch] = useState(false);
  const advisorNotesFetcher = useFetcher();

  const agendaData = followUpDataForFollowUp(interaction?.agenda);
  const advisorNotesData = followUpDataForFollowUp(interaction?.advisorNotes);

  const agendaDataIsValid =
    agendaData &&
    "content" in agendaData.parsedData &&
    agendaData.followUp.status === FollowUpStatus.Completed;
  const advisorNotesDataIsValid =
    advisorNotesData &&
    "content" in advisorNotesData.parsedData &&
    advisorNotesData.followUp.status === FollowUpStatus.Completed;

  const [agendaParsedData, setAgendaParsedData] = useState(
    agendaData?.parsedData
  );
  const [advisorNotesParsedData, setAdvisorNotesParsedData] = useState(
    advisorNotesData?.parsedData
  );

  const updateAgenda = async () => {
    if (!agendaData || !interaction) {
      return;
    }
    agendaFetcher.submit(
      {
        followUpId: agendaData?.followUp.uuid,
        followUpData: JSON.stringify(agendaParsedData),
        actionType: "update-meeting-follow-up",
      },
      {
        method: "post",
        action: `/notes/${interaction.noteUuid}`,
        encType: "multipart/form-data",
      }
    );
  };

  const updateAdvisorNotes = async () => {
    if (!advisorNotesData || !interaction) {
      return;
    }
    advisorNotesFetcher.submit(
      {
        followUpId: advisorNotesData?.followUp.uuid,
        followUpData: JSON.stringify(advisorNotesParsedData),
        actionType: "update-meeting-follow-up",
      },
      {
        method: "post",
        action: `/notes/${interaction.noteUuid}`,
        encType: "multipart/form-data",
      }
    );
  };

  const agendaBeingUpdated = agendaFetcher.state !== "idle";
  const advisorNotesBeingUpdated = advisorNotesFetcher.state !== "idle";

  // Handle the results of updating the agenda.
  useEffect(() => {
    if (agendaBeingUpdated) {
      return;
    }
    if (!(agendaFetcher.data instanceof Object)) {
      return;
    }
    if ("success" in agendaFetcher.data && agendaFetcher.data.success) {
      return;
    }
    toast.error("Could not update agenda. Please try again.");
    setAgendaEditable(true);
  }, [agendaBeingUpdated, agendaFetcher.data, setAgendaEditable]);

  const handleAgendaCopy = () => {
    if (!agendaData) {
      return;
    }
    navigator.clipboard.writeText(
      "content" in agendaData?.parsedData ? agendaData.parsedData.content : ""
    );
    toast.success("Copied to clipboard!", { autoClose: 2000 });
  };

  const handleAdvisorNotesCopy = () => {
    if (!advisorNotesData) {
      return;
    }
    navigator.clipboard.writeText(
      "content" in advisorNotesData?.parsedData
        ? advisorNotesData.parsedData.content
        : ""
    );
    toast.success("Copied to clipboard!", { autoClose: 2000 });
  };

  const refreshPage = () => {
    window.location.reload();
  };

  if (!interaction) {
    return <SkeletonMeetingPrepTab />;
  }

  const agendaChangeHandler = debounce(
    (content: string) => {
      if (!agendaParsedData || !("content" in agendaParsedData)) {
        return;
      }
      setAgendaParsedData({
        format: agendaParsedData.format,
        content,
      });
    },

    500,
    { leading: false, trailing: true }
  );

  const advisorNotesChangeHandler = debounce(
    (content: string) => {
      if (!advisorNotesParsedData || !("content" in advisorNotesParsedData)) {
        return;
      }
      setAdvisorNotesParsedData({
        format: advisorNotesParsedData.format,
        content,
      });
    },
    500,
    { leading: false, trailing: true }
  );

  return (
    <div className="space-y-2">
      {/* Top bar with clients, search and button */}
      <div className="flex w-full items-center justify-between">
        <div>
          Client details:
          {clients.length > 0 ? (
            <AttendeesList attendees={clients} className="mx-1" />
          ) : (
            <Typography variant="body2">
              No clients added to this meeting
            </Typography>
          )}
        </div>
        {clients && clients.length > 0 && clients[0]?.uuid ? (
          <AskMeAnythingForClient
            clientId={clients[0].uuid}
            query={query}
            setQuery={setQuery}
            triggerSearch={triggerSearch}
            setTriggerSearch={setTriggerSearch}
          />
        ) : null}
      </div>

      <Divider />

      {/* Main content */}
      <div className="flex w-full flex-col space-y-2 md:flex-row md:space-x-4 md:space-y-0">
        {/* Left Column: Agenda */}
        <div className="md:w-1/2">
          <div className="flex justify-between">
            <div className="flex items-center justify-start space-x-2">
              <CalendarRange />
              <Typography variant="h2">Agenda</Typography>
            </div>
            {agendaDataIsValid && (
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={handleAgendaCopy}
                  aria-label="Copy agenda"
                  disabled={agendaEditable || agendaBeingUpdated}
                >
                  <ContentCopy />
                </Button>
                {!readOnly && (
                  <Button
                    variant="ghost"
                    size="icon-sm"
                    onClick={() => {
                      const agendaWasEditable = agendaEditable;
                      setAgendaEditable(!agendaEditable);
                      if (agendaWasEditable) {
                        updateAgenda();
                      }
                    }}
                    aria-label="Edit agenda"
                    disabled={agendaBeingUpdated}
                  >
                    {!agendaBeingUpdated ? (
                      agendaEditable ? (
                        <SaveOutlined />
                      ) : (
                        <Pen />
                      )
                    ) : (
                      <Spinner />
                    )}
                  </Button>
                )}
                <GenerateAgendaEmailButton
                  agenda={agendaData.followUp}
                  emailSubject="Agenda for our upcoming meeting"
                  disabled={agendaEditable || agendaBeingUpdated}
                />
              </div>
            )}
          </div>
          <div className="mt-2">
            {agendaDataIsValid ? (
              <MarkdownEditor
                markdown={
                  agendaParsedData && "content" in agendaParsedData
                    ? agendaParsedData.content
                    : ""
                }
                onChange={agendaChangeHandler}
                showToolbar={true}
                readOnly={readOnly || !agendaEditable}
              />
            ) : readOnly ? (
              "No agenda available"
            ) : (
              <Skeleton className="flex h-64 w-full flex-col items-center justify-center px-6 text-center text-sm">
                <div>Please wait while we generate an agenda for you.</div>
                <div>This may take a moment.</div>

                <div className="mt-4">
                  If you feel like it's been too long,
                  <br />
                  try{" "}
                  <span
                    className="cursor-pointer text-primary underline"
                    onClick={refreshPage}
                  >
                    refreshing this page
                  </span>
                  .
                </div>
              </Skeleton>
            )}
          </div>
        </div>

        {/* Divider */}
        <div className="w-0 border-l border-transparent md:border-border" />

        {/* Right Column: Advisor notes */}
        <div className="md:w-1/2">
          <div className="flex justify-between">
            <div className="flex items-center justify-start space-x-2">
              <NotebookPen />
              <Typography variant="h2">Advisor notes</Typography>
            </div>
            {advisorNotesDataIsValid && (
              <div className="flex justify-end">
                <Button
                  variant="ghost"
                  size="icon-sm"
                  onClick={handleAdvisorNotesCopy}
                  aria-label="Copy advisor notes"
                  disabled={advisorNotesEditable || advisorNotesBeingUpdated}
                >
                  <ContentCopy />
                </Button>
                {!readOnly && (
                  <Button
                    variant="ghost"
                    size="icon-sm"
                    onClick={() => {
                      const advisorNotesWereEditable = advisorNotesEditable;
                      setAdvisorNotesEditable(!advisorNotesEditable);
                      if (advisorNotesWereEditable) {
                        updateAdvisorNotes();
                      }
                    }}
                    aria-label="Edit advisor notes"
                    disabled={advisorNotesBeingUpdated}
                  >
                    {!advisorNotesBeingUpdated ? (
                      advisorNotesEditable ? (
                        <SaveOutlined />
                      ) : (
                        <Pen />
                      )
                    ) : (
                      <Spinner />
                    )}
                  </Button>
                )}
              </div>
            )}
          </div>
          <div className="mt-2">
            {advisorNotesDataIsValid ? (
              <MarkdownEditor
                markdown={
                  advisorNotesParsedData && "content" in advisorNotesParsedData
                    ? advisorNotesParsedData.content
                    : ""
                }
                onChange={advisorNotesChangeHandler}
                showToolbar={true}
                readOnly={readOnly || !advisorNotesEditable}
              />
            ) : readOnly ? (
              "No advisor notes"
            ) : (
              <Skeleton className="flex h-64 w-full flex-col items-center justify-center px-6 text-center text-sm">
                <div>Please wait while we generate advisor notes for you.</div>
                <div>This may take a moment.</div>

                <div className="mt-4">
                  If you feel like it's been too long,
                  <br />
                  try{" "}
                  <span
                    className="cursor-pointer text-primary underline"
                    onClick={refreshPage}
                  >
                    refreshing this page
                  </span>
                  .
                </div>
              </Skeleton>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MeetingPrepTab;
