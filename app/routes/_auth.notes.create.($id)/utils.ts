import {
  unstable_composeUploadHandlers,
  unstable_createMemoryUploadHandler,
  unstable_parseMultipartFormData,
} from "@remix-run/node";
import { AttendeeOptions } from "~/api/attendees/types";
import { MeetingType } from "~/api/openapi/generated";

import {
  CreateNoteFormStruct,
  MEETING_TYPES,
} from "~/routes/_auth.notes.create.($id)/types";
import { formDataToObject } from "~/utils/validation";

export const getMeetingNameFromAttendees = (
  userId: string,
  attendees: AttendeeOptions,
  meetingType: MeetingType
): string => {
  // Ensure attendees is an array; if undefined, default to an empty array
  const filteredAttendees = (attendees ?? []).filter(
    (item) => item.uuid !== userId
  );

  let attendeeString: string;

  if (filteredAttendees.length > 0) {
    // Join attendee names into a sentence-like string
    attendeeString = filteredAttendees
      .map((item) => item.name)
      .join(", ")
      .replace(/,([^,]*)$/, " and$1");
  } else {
    // Fallback for when there are no other attendees
    attendeeString =
      meetingType.category === "debrief" || meetingType.category === "client"
        ? "a client"
        : "colleagues";
  }

  // Get the title prefix based on the meeting type
  const titlePrefix =
    MEETING_TYPES[meetingType.category]?.titlePrefix || "Meeting with";

  // Return the formatted meeting name
  return `${titlePrefix} ${attendeeString}`;
};

export const getPayloadFromForm = async (request: Request) => {
  // Parse formData using Remix's multipart form handlers
  // See https://remix.run/docs/en/main/utils/parse-multipart-form-data
  const formData = await unstable_parseMultipartFormData(
    request,
    unstable_composeUploadHandlers(
      unstable_createMemoryUploadHandler({
        maxPartSize: 500_000_000, // 500MB
      })
    )
  );

  return CreateNoteFormStruct.parse(formDataToObject(formData));
};

export const tutorialSteps = (enableSaveScheduledNotes: boolean) => [
  {
    title: "This is the Note Page",
    intro:
      "Here, you can review, edit, sync, or delete meeting notes. This is also where you can start an in-person meeting.",
  },
  {
    title: "Meeting Actions",
    element: "#LayoutHeader",
    intro:
      "Return to the hub by clicking the back arrow. Click on the Edit button to edit the note fields, and Save when done.",
  },
  ...(enableSaveScheduledNotes
    ? [
        {
          title: "Meeting Date and Time",
          element: "[data-onboarding='scheduled-timestamp']",
          intro:
            "As long as the meeting is pending you can update the date and start time.",
        },
      ]
    : []),
  {
    title: "Meeting Tabs",
    element: "[data-onboarding='meeting-tabs']",
    intro:
      "These tabs will be your primary navigation on the Note page. Before a meeting you’ll only see Meeting Details and Meeting Prep; after the meeting, new tabs will appear.",
  },
  {
    title: "Meeting Type",
    element: "#meetingType",
    intro:
      "This is the meeting type dropdown. Meetings default to Client but you can choose any meeting type from this list. A meeting type is required to generate Meeting Prep.",
  },
  {
    title: "Who’s attending?",
    element: "#attendees",
    intro:
      "When your calendar is integrated, everyone you invite will show up here. You can also add new people by choosing them from the drop down, typing their name, or adding their email address.",
  },
  {
    title: "How will Zeplyn join the meeting?",
    element: "[data-onboarding='audio-source']",
    intro:
      "Zeplyn can take notes during a live mic call; within virtual meeting software like Zoom, Webex, Teams, or Google Meet; or call you for a conference call.",
  },
  {
    title: "Sending the Notetaker",
    element: "#send-notetaker",
    hasDynamicElement: true,
    intro:
      "While Zeplyn can auto-join, you can also send the Notetaker directly to a meeting.",
  },
];
