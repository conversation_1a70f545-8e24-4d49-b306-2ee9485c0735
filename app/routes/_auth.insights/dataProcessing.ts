import { SerializeFrom } from "~/types/remix";
import { ChartData } from "chart.js";
import { eachDayOfInterval, format } from "date-fns";
import {
  ListNotesResponse,
  ListTasksResponse,
  NoteAudioSource,
  ProcessingStatus,
} from "~/api/openapi/generated";

export interface Filter {
  startDate: Date;
  endDate: Date;
  clientUUIDs: string[];
  advisorUUIDs: string[];
}

// Returns the notes in `notes` that have attendees that match the client or advisor UUIDs in `filter`.
//
//// An empty filter item means that the notes should not be filtered by that item.
const filterNotesByClientAndAdvisor = (
  notes: ListNotesResponse[],
  filter: Filter,
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>
) => {
  const { clientUUIDs, advisorUUIDs } = filter;
  let filteredNotes = notes;
  if (advisorUUIDs.length > 0) {
    filteredNotes = notes.filter((note) => {
      return note.attendees.some((attendee) => {
        const userUUID = attendeeUUIDToUserUUID[attendee.uuid];
        return userUUID ? advisorUUIDs.includes(userUUID) : false;
      });
    });
  }
  if (clientUUIDs.length > 0) {
    filteredNotes = notes.filter((note) => {
      return (
        note.attendees.some((attendee) => {
          const clientUUID = attendeeUUIDToClientUUID[attendee.uuid];
          return clientUUID ? clientUUIDs.includes(clientUUID) : false;
        }) ||
        (note.client?.uuid && clientUUIDs.includes(note.client.uuid))
      );
    });
  }
  return filteredNotes;
};

// Returns a chart data object for the number of notes by date.
export const getNotesByDate = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "EEE M/dd/yyyy")
  );

  const notesCountByDate: Record<string, number> = dateRange.reduce(
    (acc, date) => {
      acc[date] = 0;
      return acc;
    },
    {} as Record<string, number>
  );

  filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).forEach((note) => {
    const date = note.scheduledStartTime || note.created;
    const formattedDate = format(new Date(date), "EEE M/dd/yyyy");
    if (notesCountByDate[formattedDate] !== undefined) {
      notesCountByDate[formattedDate] += 1;
    }
  });

  const nonEmptyDateRange = dateRange.filter(
    (date) => notesCountByDate[date] !== 0
  );

  return {
    labels: nonEmptyDateRange.map((date) => format(new Date(date), "EEE M/dd")),
    datasets: [
      {
        data: nonEmptyDateRange.map((date) => notesCountByDate[date] ?? 0),
      },
    ],
  };
};

// Returns a chart data object for the number of notes per advisor.
//
// Advisors are filtered by the `advisorUUIDs` in `filter`. They are read from the note's attendees,
// so one meeting can be counted for multiple advisors.
export const getNotesByAdvisor = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  userUUIDToUserName: Record<string, string>,
  filter: Filter
): ChartData<"line"> => {
  const { startDate, endDate, advisorUUIDs } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "M/dd/yyyy")
  );

  const notesByOwner = filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const formattedDate = format(new Date(date), "M/dd/yyyy");
    note.attendees.forEach((attendee) => {
      const userUUID = attendeeUUIDToUserUUID[attendee.uuid];
      if (!userUUID) {
        return;
      }
      if (advisorUUIDs.length > 0 && !advisorUUIDs.includes(userUUID)) {
        return;
      }
      if (!acc[userUUID]) {
        acc[userUUID] = Object.fromEntries(dateRange.map((date) => [date, 0]));
      }
      if (acc[userUUID]?.[formattedDate] !== undefined) {
        acc[userUUID]![formattedDate] += 1;
      }
    });
    return acc;
  }, {} as Record<string, Record<string, number>>);

  const datasets = Object.entries(notesByOwner).map(([owner, notes]) => ({
    label: userUUIDToUserName[owner] ?? "Unknown",
    data: dateRange.map((date) => notes[date] ?? 0),
  }));

  return {
    labels: dateRange.map((date) => format(new Date(date), "M/dd")),
    datasets,
  };
};

// Returns a chart data object for the number of notes by input method (mic, video, phone).
export const getNotesByAudioSource = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
): ChartData<"pie"> => {
  const { startDate, endDate } = filter;
  const methodsCount = filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const noteDate = new Date(date);
    if (noteDate >= startDate && noteDate <= endDate) {
      const source = note.audioSource;
      acc[source] = (acc[source] ?? 0) + 1;
    }
    return acc;
  }, {} as Record<NoteAudioSource, number>);

  const methodMap: Record<NoteAudioSource, string> = {
    [NoteAudioSource.VideoCall]: "Virtual (video call)",
    [NoteAudioSource.Mic]: "In-person",
    [NoteAudioSource.Phone]: "Phone call",
  };

  const labels = Object.keys(methodMap) as NoteAudioSource[];
  const data = labels.map((label) => methodsCount[label] ?? 0);

  const total = data.reduce((acc, count) => acc + count, 0);
  const percentagesPerLabel = labels.reduce((acc, label) => {
    acc[label] = Math.round(((methodsCount[label] ?? 0) / total) * 100);
    return acc;
  }, {} as Record<NoteAudioSource, number>);

  return {
    labels: labels.map((label) => {
      let l = methodMap[label];
      const percentage = percentagesPerLabel[label];
      if (percentage && !isNaN(percentage)) {
        l += ` (${percentage}%)`;
      }
      return l;
    }),
    datasets: [{ data }],
  };
};

// Returns a chart data object for the number of notes by status.
export const getNotesByStatus = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
): ChartData<"pie"> => {
  const { startDate, endDate } = filter;
  const processingStatusToDisplay: Record<
    ProcessingStatus,
    string | undefined
  > = {
    [ProcessingStatus.Scheduled]: undefined,
    [ProcessingStatus.Uploaded]: undefined,
    [ProcessingStatus.Unknown]: undefined,
    [ProcessingStatus.Missing]: undefined,
    [ProcessingStatus.Processed]: "Pending Review",
    [ProcessingStatus.Finalized]: "Synced",
  };

  const statusCount: Record<string, number> = filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const noteDate = new Date(date);
    if (noteDate >= startDate && noteDate <= endDate) {
      let status = processingStatusToDisplay[note.status];
      if (note.tags.includes("Empty note")) {
        status = "Empty";
      }
      if (!status) {
        return acc;
      }
      acc[status] = (acc[status] ?? 0) + 1;
    }
    return acc;
  }, {} as Record<string, number>);

  const labels = Object.keys(statusCount);
  const data = labels.map((label) => statusCount[label] || 0);

  const total = data.reduce((acc, count) => acc + count, 0);
  const percentagesPerLabel = labels.reduce((acc, label) => {
    acc[label] = Math.round(((statusCount[label] ?? 0) / total) * 100);
    return acc;
  }, {} as Record<string, number>);

  return {
    labels: labels.map((label) => `${label} (${percentagesPerLabel[label]}%)`),
    datasets: [{ data }],
  };
};

export const getTasksByAssignee = (
  tasks: SerializeFrom<ListTasksResponse>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const tasksByAssignee = tasks.data
    .filter((task) => {
      const filterUUIDs = filter.clientUUIDs.concat(filter.advisorUUIDs);
      if (filterUUIDs.length == 0) {
        return true;
      }

      return task.assignee &&
        (filterUUIDs.includes(task.assignee.uuid) ||
          filterUUIDs.includes(task.assignee.uuid) ||
          task.assignees?.find((assignee) =>
            filterUUIDs.includes(assignee.uuid)
          ))
        ? true
        : false;
    })
    .reduce((acc, task) => {
      const taskDate = new Date(task.created);
      if (taskDate >= startDate && taskDate <= endDate) {
        const assignee = task.assignee?.name ?? "No assignee specified";
        acc[assignee] = (acc[assignee] ?? 0) + 1;
      }
      return acc;
    }, {} as Record<string, number>);

  const labels = Object.keys(tasksByAssignee);
  const data = labels.map((label) => tasksByAssignee[label] || 0);

  return {
    labels,
    datasets: [{ data }],
  };
};

// Returns a chart data object for the total duration of notes by advisor and day.
export const getTotalDurationByAdvisorAndDay = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
): ChartData<"line"> => {
  const { startDate, endDate, advisorUUIDs } = filter;
  const dateRange = eachDayOfInterval({ start: startDate, end: endDate }).map(
    (date) => format(date, "M/dd/yyyy")
  );

  const durationByAdvisorAndDay = filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const formattedDate = format(new Date(date), "M/dd/yyyy");
    const duration = note.meetingDurationSeconds || 0;

    note.attendees.forEach((attendee) => {
      const advisorUUID = attendeeUUIDToUserUUID[attendee.uuid];
      if (!advisorUUID) {
        return;
      }

      if (advisorUUIDs.length > 0 && !advisorUUIDs.includes(advisorUUID)) {
        return;
      }

      if (!acc[advisorUUID]) {
        acc[advisorUUID] = {
          name: attendee.name,
          durations: Object.fromEntries(dateRange.map((date) => [date, 0])),
        };
      }

      if (acc[advisorUUID]?.durations[formattedDate] !== undefined) {
        acc[advisorUUID]!.durations[formattedDate]! += duration;
      }
    });

    return acc;
  }, {} as Record<string, { name: string; durations: Record<string, number> }>);

  const datasets = Object.values(durationByAdvisorAndDay).map(
    ({ name, durations }) => ({
      label: name,
      data: dateRange.map((date) => Math.round((durations[date] ?? 0) / 60)),
    })
  );

  return {
    labels: dateRange.map((date) => format(new Date(date), "M/dd")),
    datasets,
  };
};

export const getClientCountByOwner = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
): ChartData<"bar"> => {
  const { startDate, endDate } = filter;
  const clientCountByOwner = filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const noteDate = new Date(date);
    if (noteDate >= startDate && noteDate <= endDate) {
      const advisorAttendees = note.attendees.filter(
        (attendee) => attendeeUUIDToUserUUID[attendee.uuid]
      );
      const clientAttendees = note.attendees.filter(
        (attendee) => attendeeUUIDToClientUUID[attendee.uuid]
      );

      advisorAttendees.forEach((attendee) => {
        const advisorUUID = attendeeUUIDToUserUUID[attendee.uuid];
        if (!advisorUUID) {
          return;
        }

        if (!acc[advisorUUID]) {
          acc[advisorUUID] = {
            name: attendee.name,
            clients: new Set<string>(),
          };
        }

        clientAttendees.forEach((clientAttendee) => {
          const clientUUID = attendeeUUIDToClientUUID[clientAttendee.uuid];
          if (clientUUID) {
            acc[advisorUUID]?.clients.add(clientAttendee.name);
          }
        });
      });
    }
    return acc;
  }, {} as Record<string, { name: string; clients: Set<string> }>);

  const result = Object.entries(clientCountByOwner).reduce(
    (acc, [ownerUuid, { name, clients }]) => {
      acc[ownerUuid] = { name, clientCount: clients.size };
      return acc;
    },
    {} as Record<string, { name: string; clientCount: number }>
  );

  const labels = Object.values(result).map((owner) => owner.name);
  const data = Object.values(result).map((owner) => owner.clientCount);

  return {
    labels,
    datasets: [
      {
        data,
      },
    ],
  };
};

export const getTagCounts = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
) => {
  const { startDate, endDate } = filter;
  return filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const noteDate = new Date(date);
    if (noteDate >= startDate && noteDate <= endDate) {
      note.tags.forEach((tag) => {
        acc[tag] = (acc[tag] ?? 0) + 1;
      });
    }
    return acc;
  }, {} as Record<string, number>);
};

export const getTagsByClient = (
  notes: ListNotesResponse[],
  attendeeUUIDToClientUUID: Record<string, string>,
  attendeeUUIDToUserUUID: Record<string, string>,
  filter: Filter
) => {
  const { startDate, endDate } = filter;
  const tagsByClient = filterNotesByClientAndAdvisor(
    notes,
    filter,
    attendeeUUIDToClientUUID,
    attendeeUUIDToUserUUID
  ).reduce((acc, note) => {
    const date = note.scheduledStartTime || note.created;
    const noteDate = new Date(date);
    if (noteDate >= startDate && noteDate <= endDate) {
      note.attendees.forEach((attendee) => {
        const clientUUID = attendeeUUIDToClientUUID[attendee.uuid];
        if (!clientUUID) {
          return;
        }
        if (!acc[clientUUID]) {
          acc[clientUUID] = {
            name: attendee.name ?? "Unnamed client",
            tags: {},
          };
        }
        note.tags.forEach((tag) => {
          acc[clientUUID]!.tags[tag] = (acc[clientUUID]?.tags[tag] ?? 0) + 1;
        });
      });
      const client = note.client;
      if (client) {
        if (!acc[client.uuid]) {
          acc[client.uuid] = {
            name: client.name ?? "Unnamed client",
            tags: {},
          };
        }
        note.tags.forEach((tag) => {
          acc[client.uuid]!.tags[tag] = (acc[client.uuid]?.tags[tag] ?? 0) + 1;
        });
      }
    }
    return acc;
  }, {} as Record<string, { name: string; tags: Record<string, number> }>);

  return Object.entries(tagsByClient).reduce(
    (acc, [clientUuid, { name, tags }]) => {
      const sortedTags = Object.entries(tags).sort(
        ([nameOne, countOne], [nameTwo, countTwo]) => {
          const countDiff = countTwo - countOne;
          return countDiff !== 0 ? countDiff : nameOne.localeCompare(nameTwo);
        }
      );
      acc[clientUuid] = { name, tags: sortedTags.map(([tag]) => tag) };
      return acc;
    },
    {} as Record<string, { name: string; tags: string[] }>
  );
};
