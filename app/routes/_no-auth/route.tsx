import { datadogLogs } from "@datadog/browser-logs";
import { datadogRum } from "@datadog/browser-rum";
import { type LoaderFunctionArgs } from "@remix-run/node";
import { Outlet } from "@remix-run/react";
import { authenticator } from "~/auth/authenticator.server";

// Exports
export const loader = async ({ request }: LoaderFunctionArgs) => {
  // On page load, check if user is already authenticated and redirect them to
  // /notes if they are already logged in
  await authenticator.isAuthenticated(request, {
    successRedirect: "/",
  });

  return null;
};

const Route = () => {
  datadogRum.clearUser();
  datadogLogs.clearUser();
  return <Outlet />;
};
export default Route;
