import { json, redirect, LoaderFunctionArgs } from "@remix-run/node";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";

// Exports
export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  if (!params.id) throw Error('Missing route parameter "id"');
  const taskId = params.id;

  try {
    const configuration = new Configuration(
      await configurationParameters(request)
    );
    await new TaskApi(configuration).taskDeleteTask({ taskUuid: taskId });
  } catch {
    return json({ error: "Failed to delete task" }, { status: 500 });
  }

  return redirect(`/tasks`);
};
