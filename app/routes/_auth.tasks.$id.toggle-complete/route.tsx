import { json, redirect, type LoaderFunctionArgs } from "@remix-run/node";
import { configurationParameters } from "~/api/openapi/configParams";
import { Configuration, TaskApi } from "~/api/openapi/generated";
import { logError } from "~/utils/log.server";

// Constants
const ERROR_MISSING_PARAMETER = 'Missing route parameter "id"';

export const action = async ({ params, request }: LoaderFunctionArgs) => {
  try {
    if (!params.id) throw Error(ERROR_MISSING_PARAMETER);

    // Load task details
    const taskAPI = new TaskApi(
      new Configuration(await configurationParameters(request))
    );
    const task = await taskAPI.taskViewTask({ taskUuid: params.id });

    await taskAPI.taskEditTask({
      taskUuid: task.uuid,
      taskUpdate: {
        completed: !task.completed,
        // Pass up the parent note UUID, because the task editing API treats an empty parent note
        // UUID as a request to remove the parent note.
        parentNoteUuid: task.parentNoteUuid,
      },
    });

    task.completed = !task.completed;
    return json({ task });
  } catch (error) {
    logError("app/routes/tasks.$id.toggle-complete.tsx error", error);
    return json({ error: "Failed to update task" }, { status: 500 });
  }
};

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  if (!params.id) throw Error(ERROR_MISSING_PARAMETER);

  return redirect(`/tasks/${params.id}`);
};
