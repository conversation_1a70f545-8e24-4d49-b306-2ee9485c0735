import {
  SectionItemAcknowledgementField,
  SectionItemBooleanField,
  SectionItemFieldType,
  SectionItemIntegrationCards,
  SectionItemMultiChoiceField,
  SectionItemSingleChoiceField,
  SectionItemTextField,
} from "~/api/openapi/generated";

import TextField from "./SettingTypeTextField";
import SwitchField from "./SettingTypeSwitch";
import AcknowledgementField from "./SettingTypeAcknowledgement";
import IntegrationCards from "./SettingTypeIntegrationCards";
import Dropdown from "./SettingTypeDropdown";
import SettingTypeLink from "./SettingTypeLink";

type SettingTypeWidgetItemProps = SectionItemAcknowledgementField &
  SectionItemBooleanField &
  SectionItemIntegrationCards &
  SectionItemMultiChoiceField &
  SectionItemSingleChoiceField &
  SectionItemTextField;

const SettingTypeWidget = ({
  item,
  onChange,
}: {
  item: SettingTypeWidgetItemProps;
  onChange: (id: string, value: any) => void;
}) => {
  const { kind, ...rest } = item;
  switch (kind) {
    case SectionItemFieldType.TextField:
      return <TextField onChange={onChange} {...rest} />;
    case SectionItemFieldType.IntegrationCards:
      return <IntegrationCards {...rest} />;
    case SectionItemFieldType.BooleanField:
      return <SwitchField onChange={onChange} {...rest} />;
    case SectionItemFieldType.AcknowledgementField:
      return <AcknowledgementField onChange={onChange} {...rest} />;
    case SectionItemFieldType.SingleChoiceField:
      return <Dropdown onChange={onChange} {...rest} />;
    case SectionItemFieldType.Link:
      return <SettingTypeLink {...rest} />;
    default:
      return <div>You missed one: {kind}!</div>;
  }
};

export default SettingTypeWidget;
