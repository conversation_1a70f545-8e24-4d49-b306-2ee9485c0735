import {
  redirect,
  json,
  type LoaderFunctionArgs,
  type MetaFunction,
  type ActionFunctionArgs,
} from "@remix-run/node";
import {
  EmailOutlined,
  LockOutlined,
  VisibilityOffOutlined,
  VisibilityOutlined,
} from "@mui/icons-material";
import {
  Form,
  Link as RouterLink,
  useActionData,
  useLoaderData,
} from "@remix-run/react";
import { z } from "zod";
import {
  flattenZodErrors,
  formDataToObject,
  searchParamsToObject,
} from "~/utils/validation";
import { Link } from "~/@ui/Link";
import { Button } from "~/@shadcn/ui/button";
import { LayoutStandalone } from "~/@ui/layout/LayoutStandalone";
import { logError } from "~/utils/log.server";
import { FormAlertsStack, useFormErrors } from "~/@ui/FormAlertsStack";
import { useState } from "react";
import { Typography } from "~/@ui/Typography";
import {
  FormControl,
  FormDescription,
  FormField,
  FormLabel,
  FormMessage,
} from "~/@shadcn/ui/form";
import { Input } from "~/@shadcn/ui/input";
import { AuthApi, Configuration } from "~/api/openapi/generated";
import { nonAuthenticatedConfigurationParameters } from "~/api/openapi/configParams";

// Types
const ResetPasswordFormStruct = z
  .object({
    code: z.string().min(1, "Missing reset code"),
    email: z.string().min(1, "Email required").email("Invalid email"),
    password1: z.string().min(3, "Invalid password"),
    password2: z.string().min(3, "Password confirmation required"),
  })
  .refine((obj) => obj.password1 === obj.password2, "Passwords do not match");

const QueryParamsStruct = z.object({
  email: z.string().email("Missing or invalid email"),
  code: z.string().min(1, "Missing or invalid reset code"),
});

// Exports
export const meta: MetaFunction = () => [
  { title: "Reset password" },
  { name: "description", content: "Reset account passwrod" },
];

export const action = async ({ request }: ActionFunctionArgs) => {
  // Attempt to authenticate user using email strategy
  try {
    // Validate form values
    const validatedFormData = ResetPasswordFormStruct.parse(
      formDataToObject(await request.formData())
    );

    await new AuthApi(
      new Configuration(await nonAuthenticatedConfigurationParameters())
    ).authResetPassword({
      email: validatedFormData.email,
      code: validatedFormData.code,
      newPassword: validatedFormData.password1,
    });
    return redirect("/auth/login?reset-password-success=true");
  } catch (error) {
    // Form validation errors
    if (error instanceof z.ZodError) {
      return json({ errors: flattenZodErrors(error) }, { status: 400 });
    }

    // Generic errors
    return json(
      {
        errors: [
          "Something went wrong, please try resetting your password again <NAME_EMAIL>",
        ],
      },
      { status: 500 }
    );
  }
};

export const loader = async ({ request }: LoaderFunctionArgs) => {
  // Read and validate query params
  try {
    const url = new URL(request.url);
    const validatedSearchParams = QueryParamsStruct.parse(
      searchParamsToObject(url.searchParams)
    );

    return json(validatedSearchParams);
  } catch (error) {
    logError("app/routes/auth.reset-password.tsx loader", error);
    return redirect("/auth/login?reset-password-error=true");
  }
};

const Route = () => {
  const { email, code } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const formErrors = useFormErrors(actionData);
  const [showPassword1, setShowPassword1] = useState(false);
  const [showPassword2, setShowPassword2] = useState(false);

  return (
    <LayoutStandalone title="Reset password">
      <Form
        id="resetPasswordForm"
        className="flex flex-col self-stretch"
        method="post"
      >
        <FormAlertsStack errors={formErrors} />
        <input id="code" name="code" type="hidden" value={code} />
        <FormField name="email" required>
          <FormLabel>Email</FormLabel>
          <FormControl>
            <Input
              size="lg"
              placeholder="Your email address"
              type="email"
              defaultValue={email}
              readOnly
              leftIcon={<EmailOutlined />}
            />
          </FormControl>
          <FormDescription className="flex justify-between">
            <span>&nbsp;</span>
          </FormDescription>
          <FormMessage />
        </FormField>

        <FormField name="password1" required>
          <FormLabel>Password</FormLabel>
          <FormControl>
            <Input
              size="lg"
              placeholder="Your password"
              type={showPassword1 ? "text" : "password"}
              leftIcon={<LockOutlined />}
              rightIcon={
                <Button
                  title={showPassword1 ? "Hide password" : "Show password"}
                  variant="ghost"
                  size="icon-sm"
                  onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    setShowPassword1((prev) => !prev);
                  }}
                >
                  {showPassword1 ? (
                    <VisibilityOffOutlined />
                  ) : (
                    <VisibilityOutlined />
                  )}
                </Button>
              }
            />
          </FormControl>
          <FormDescription>
            <span>&nbsp;</span>
          </FormDescription>
          <FormMessage />
        </FormField>

        <FormField name="password2" required>
          <FormLabel>Confirm password</FormLabel>
          <FormControl>
            <Input
              size="lg"
              placeholder="Confirm your password"
              type={showPassword2 ? "text" : "password"}
              leftIcon={<LockOutlined />}
              rightIcon={
                <Button
                  title={showPassword2 ? "Hide password" : "Show password"}
                  variant="ghost"
                  size="icon-sm"
                  onClick={(event) => {
                    event.preventDefault();
                    event.stopPropagation();
                    setShowPassword2((prev) => !prev);
                  }}
                >
                  {showPassword2 ? (
                    <VisibilityOffOutlined />
                  ) : (
                    <VisibilityOutlined />
                  )}
                </Button>
              }
            />
          </FormControl>
          <FormDescription>
            <span>&nbsp;</span>
          </FormDescription>
          <FormMessage />
        </FormField>

        <div className="flex flex-col self-stretch px-0 py-6">
          <Button type="submit" size="lg">
            Reset password
          </Button>
        </div>

        <Typography variant="body2" color="secondary" className="text-center">
          Go back to{" "}
          <Link asChild>
            <RouterLink to="/auth/login">Login</RouterLink>
          </Link>
        </Typography>
      </Form>
    </LayoutStandalone>
  );
};

export default Route;
