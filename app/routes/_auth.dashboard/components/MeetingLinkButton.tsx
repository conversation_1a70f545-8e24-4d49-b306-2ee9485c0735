import { Mic } from "lucide-react";
import {
  Too<PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  Toolt<PERSON><PERSON>rovider,
  TooltipTrigger,
} from "~/@shadcn/ui/tooltip";
import { Link } from "@remix-run/react";
import { Button } from "~/@shadcn/ui/button";
import { GoogleMeetIcon } from "~/@ui/assets/GoogleMeetIcon";
import { MicrosoftTeamsIcon } from "~/@ui/assets/MicrosoftTeamsIcon";
import { WebexIcon } from "~/@ui/assets/WebexIcon";
import { ZoomIcon } from "~/@ui/assets/ZoomIcon";

const iconForMeetingLink = (meetingLink: string | undefined) => {
  if (!meetingLink) return undefined;
  if (meetingLink.includes("google.com")) return GoogleMeetIcon;
  if (meetingLink.includes("zoom.us")) return ZoomIcon;
  if (meetingLink.includes("teams.microsoft.com")) return MicrosoftTeamsIcon;
  if (meetingLink.includes("webex.com")) return WebexIcon;
  return undefined;
};

const MeetingLinkButton = ({
  meetingLink,
  shouldLinkToMeeting,
}: {
  meetingLink?: string | { pathname: string; search?: string };
  shouldLinkToMeeting?: boolean;
}) => {
  const isMicRecordingLink = meetingLink instanceof Object;
  const IconComponent = isMicRecordingLink
    ? Mic
    : iconForMeetingLink(meetingLink);
  if (!IconComponent) return null;
  return shouldLinkToMeeting && meetingLink ? (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button variant="outline_primary" size="default" asChild>
            <Link to={meetingLink} target="_blank">
              <IconComponent className="!h-5 !w-5" />
              {isMicRecordingLink ? "Start meeting" : "Join meeting"}
            </Link>
          </Button>
        </TooltipTrigger>
        <TooltipContent>Join meeting</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  ) : (
    <IconComponent className="h-6 w-6" />
  );
};

export default MeetingLinkButton;
