import React, { useState, useRef, useEffect } from "react";
import { ExpandMore, ExpandLess } from "@mui/icons-material";

export const Accordion = ({
  title,
  children,
}: {
  title: string;
  children: React.ReactNode;
}) => {
  const [isOpen, setIsOpen] = useState(true);
  const contentRef = useRef<HTMLDivElement>(null);
  const [height, setHeight] = useState("0px");
  const [reduceMotion, setReduceMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia("(prefers-reduced-motion: reduce)");
    setReduceMotion(mediaQuery?.matches);

    const handleChange = () => setReduceMotion(mediaQuery?.matches);
    mediaQuery?.addEventListener("change", handleChange);

    return () => {
      mediaQuery?.removeEventListener("change", handleChange);
    };
  }, []);

  useEffect(() => {
    if (contentRef.current) {
      if (isOpen) {
        if (reduceMotion) {
          setHeight("auto");
        } else {
          setHeight(`${contentRef.current.scrollHeight}px`);
        }
      } else {
        if (reduceMotion) {
          setHeight("0px");
        } else {
          if (height === "auto") {
            setHeight(`${contentRef.current.scrollHeight}px`);
            requestAnimationFrame(() => {
              setHeight("0px");
            });
          } else {
            setHeight("0px");
          }
        }
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, children, reduceMotion]);

  useEffect(() => {
    if (isOpen && contentRef.current && !reduceMotion) {
      const handleTransitionEnd = () => {
        setHeight("auto");
        if (contentRef.current) {
          contentRef.current.removeEventListener(
            "transitionend",
            handleTransitionEnd
          );
        }
      };

      contentRef.current.addEventListener("transitionend", handleTransitionEnd);

      return () => {
        // eslint-disable-next-line react-hooks/exhaustive-deps
        if (contentRef.current) {
          // eslint-disable-next-line react-hooks/exhaustive-deps
          contentRef.current.removeEventListener(
            "transitionend",
            handleTransitionEnd
          );
        }
      };
    }
  }, [isOpen, reduceMotion]);

  const toggleAccordion = () => setIsOpen((prev) => !prev);

  return (
    <div data-testid="accordion-container" className="mb-4">
      <div
        className="bg-light-blue flex cursor-pointer items-center gap-1 p-2"
        onClick={toggleAccordion}
        aria-expanded={isOpen}
        aria-controls={`accordion-content-${title}`}
      >
        {isOpen ? <ExpandLess /> : <ExpandMore />}
        <span>{title}</span>
      </div>

      <div
        data-testid="accordion-content"
        id={`accordion-content-${title}`}
        className={`overflow-hidden ${
          reduceMotion ? "" : "transition-[height] duration-300 ease-in-out"
        }`}
        style={{
          height: height,
        }}
      >
        <div ref={contentRef} className="bg-white p-2">
          {children}
        </div>
      </div>
    </div>
  );
};
