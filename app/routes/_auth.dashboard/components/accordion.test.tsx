import { render, screen, waitFor } from "@testing-library/react";
import { Accordion } from "./Accordion";
import "@testing-library/jest-dom";
import userEvent from "@testing-library/user-event";

describe("Accordion", () => {
  beforeEach(() => {
    Object.defineProperty(HTMLElement.prototype, "scrollHeight", {
      configurable: true,
      value: 100,
    });
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("renders the title and children when open by default", () => {
    render(
      <Accordion title="Test Accordion">
        <p>Accordion Content</p>
      </Accordion>
    );

    // Check that the title is rendered
    expect(screen.getByText("Test Accordion")).toBeInTheDocument();

    // Check that the content is visible by default
    expect(screen.getByText("Accordion Content")).toBeInTheDocument();
  });

  it("toggles the content visibility when clicked", async () => {
    render(
      <Accordion title="Toggle Accordion">
        <p>Hidden Content</p>
      </Accordion>
    );

    const titleElement = screen.getByText("Toggle Accordion");
    const contentElement = screen.getByText("Hidden Content");
    const accordionContent = contentElement.closest(
      "div[id^='accordion-content-']"
    );

    // Initially Open
    expect(accordionContent).toHaveStyle("height: 100px");

    const user = userEvent.setup();

    // Click to open
    await user.click(titleElement);

    // After closing, height should be '0px' (mocked scrollHeight)
    expect(accordionContent).toHaveStyle("height: 0px");

    // Click to close
    await user.click(titleElement);

    // Wait for height to be '100px' after opening
    await waitFor(() => {
      expect(accordionContent).toHaveStyle("height: 100px");
    });
  });

  it("applies transitions when reduced motion is not preferred", () => {
    const matchMediaMock = vi.fn().mockImplementation((query) => {
      return {
        matches: false,
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
    });
    window.matchMedia = matchMediaMock;

    render(
      <Accordion title="Smooth Accordion">
        <p>Animated Content</p>
      </Accordion>
    );

    const container = screen.getByTestId("accordion-content");
    expect(container).toHaveClass(
      "transition-[height] duration-300 ease-in-out"
    );
  });

  it("removes transitions if user prefers reduced motion", () => {
    // Mock matchMedia to simulate reduced motion preference
    const matchMediaMock = vi.fn().mockImplementation((query) => {
      return {
        matches: true,
        media: query,
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
      };
    });
    window.matchMedia = matchMediaMock;

    render(
      <Accordion title="No Motion Accordion">
        <p>No animation</p>
      </Accordion>
    );

    const container = screen.getByText("No Motion Accordion").closest("div");

    // If reduced motion is enabled, no transition classes should be applied
    expect(container).not.toHaveClass("transition-all");
  });

  it("updates height when children change", () => {
    const { rerender } = render(
      <Accordion title="Dynamic Accordion">
        <p>Initial Content</p>
      </Accordion>
    );

    // Initial content present
    expect(screen.getByText("Initial Content")).toBeInTheDocument();

    // Rerender with different content
    rerender(
      <Accordion title="Dynamic Accordion">
        <p>New Content</p>
      </Accordion>
    );

    // Check that the new content is displayed
    expect(screen.getByText("New Content")).toBeInTheDocument();
    expect(screen.queryByText("Initial Content")).not.toBeInTheDocument();
  });
});
