export const tutorialSteps = [
  {
    title: "Welcome",
    intro: "We’re excited to have you join Zeplyn! Let’s take a quick tour!",
  },
  {
    title: "Main Navigation",
    element: "#LayoutNavBar-upper",
    intro:
      "This navigation will help you find your way around. The top button will return you here from anywhere in the application. Under this you’ll see Notes, Tasks, then Clients.",
  },
  {
    title: "Help and Settings",
    element: "[data-onboarding='usermenu']",
    intro:
      "Click the profile button to see options like Settings, Help and Logout.",
  },
  {
    title: "Welcome to your Hub!",
    element: "[data-onboarding='meeting-tabs']",
    intro:
      "On the Hub, meetings are the focus. These three tabs will show you today’s meetings, upcoming meetings (you’ll set how far into the future this looks on <PERSON><PERSON><PERSON>), and past meetings that still need your attention.",
  },
  {
    title: "Today’s Meeting",
    element: "#onboarding-scheduled-card",
    intro:
      "Learn how to use <PERSON><PERSON><PERSON> with our demo meeting. Click the Join Meeting button for virtual meetings or Start Meeting for in person/phone meetings.",
  },
  {
    title: "Meeting Prep",
    element: "#onboarding-prep-cta",
    intro: "The Meeting Prep button allows you to prep for your meeting.",
  },
];
