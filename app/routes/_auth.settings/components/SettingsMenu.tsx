import { Link, useParams } from "@remix-run/react";
import {
  AddLinkOutlined,
  LabelOutlined,
  Person2Outlined,
  SettingsOutlined,
  SupervisedUserCircleOutlined,
} from "@mui/icons-material";

import { MenuItem, MenuItemId } from "~/api/openapi/generated";
import { cn } from "~/@shadcn/utils";

type SettingsMenuType = {
  menuData: MenuItem[] | undefined;
};
const SettingsMenu = ({ menuData }: SettingsMenuType) => {
  return (
    <>
      {menuData?.map((item) => (
        <MenuRowItem key={item.id} item={item} isOuterMost />
      ))}
    </>
  );
};

const MenuRowItem = ({
  item,
  isOuterMost,
}: {
  item: MenuItem;
  isOuterMost?: boolean;
}) => {
  const { id, label, items } = item;
  const MenuIcon = getIcon(id);

  const params = useParams();
  const isActiveLink = params["*"] === id;

  const content = (
    <>
      <div className={cn("flex items-center", isActiveLink && "text-primary")}>
        {!isOuterMost && <MenuIcon className="h-6 w-6" />}
        <span
          className={cn(
            "ml-1 whitespace-nowrap",
            isOuterMost && "text-2xl font-semibold"
          )}
        >
          {label}
        </span>
      </div>
      {items?.map((item) => (
        <div className="ml-4" key={item.id}>
          <MenuRowItem item={item} />
        </div>
      ))}
    </>
  );

  // don't show links for outer-most items
  if (!isOuterMost) {
    return (
      <Link to={`/settings/${id}`} className="mt-4 flex items-center">
        {content}
      </Link>
    );
  }

  return <div className="mt-4 flex flex-col">{content}</div>;
};

function getIcon(id: string) {
  switch (id) {
    // my account
    case MenuItemId.Integrations:
      return AddLinkOutlined;
    case MenuItemId.Settings:
      return SettingsOutlined;
    case MenuItemId.ProfileDetails:
      return Person2Outlined;

    // admin
    case MenuItemId.UserImpersonation:
      return SupervisedUserCircleOutlined;

    default:
      return LabelOutlined;
  }
}

export default SettingsMenu;
