import {
  AccessTimeOutlined,
  EventOutlined,
  PeopleOutlined,
} from "@mui/icons-material";
import {
  json,
  redirect,
  type LoaderFunctionArgs,
  type MetaFunction,
} from "@remix-run/node";
import { useFetcher, useLoaderData, useRevalidator } from "@remix-run/react";
import { format } from "date-fns";
import { getNoteById } from "~/api/notes/getNoteById.server";
import { BackButton } from "~/@ui/buttons/BackButton";
import { EditButton } from "~/@ui/buttons/EditButton";
import { cn } from "~/@shadcn/utils";
import { Typography } from "~/@ui/Typography";
import { Checkbox } from "~/@shadcn/ui/checkbox";
import { NoteCard } from "~/@ui/notes/NoteCard";
import { HeaderV2, SidebarV2, PageTitleText } from "~/@ui/layout/LayoutV2";
import { Separator } from "~/@shadcn/ui/separator";
import { toast } from "react-toastify";
import {
  Configuration,
  NoteResponse,
  ProcessingStatus,
  TaskApi,
} from "~/api/openapi/generated";
import { SerializeFrom } from "~/types/remix";
import { AfterHydration } from "~/utils/hydration";
import { configurationParameters } from "~/api/openapi/configParams";

// Constants
const ERROR_MISSING_PARAMETER = 'Missing route parameter "id"';

// Fragments
const SidebarHeader = ({ taskId }: { taskId: string }) => (
  <HeaderV2
    className="lg:gap-0"
    left={
      <BackButton className="lg:hidden" to="/tasks" tooltip="Back to tasks" />
    }
    right={
      <div className="flex flex-row gap-2">
        <EditButton to={`/tasks/${taskId}/edit`} tooltip="Edit task" />
      </div>
    }
  />
);

// Exports
export const meta: MetaFunction<typeof loader> = ({ data }) => {
  return [
    { title: `Task - ${data?.task.title}` },
    { name: "description", content: "View task details" },
  ];
};

export const loader = async ({ params, request }: LoaderFunctionArgs) => {
  if (!params.id) throw Error(ERROR_MISSING_PARAMETER);

  // Load task details
  try {
    const configuration = new Configuration(
      await configurationParameters(request)
    );
    const task = await new TaskApi(configuration).taskViewTask({
      taskUuid: params.id,
    });
    const parentNote = task.parentNoteUuid
      ? await getNoteById({ noteId: task.parentNoteUuid, request })
      : null;

    return json({ task, parentNote });
  } catch {
    return redirect("/tasks");
  }
};

const Route = () => {
  const { task, parentNote } = useLoaderData<typeof loader>();
  const { submit } = useFetcher();
  const revalidator = useRevalidator();
  return (
    <SidebarV2
      favorSidebarOnMobile
      header={<SidebarHeader taskId={task.uuid} />}
    >
      <div className="flex flex-col gap-3 self-stretch px-6 pb-6">
        {/* Task Title and Completion Checkbox */}
        <div className="flex items-center gap-2">
          <Checkbox
            className="mt-2"
            checked={task.completed}
            onCheckedChange={() => {
              if (!navigator.onLine) {
                toast.error("You are offline. Please check your connection.");
                return;
              }
              submit(null, {
                method: "post",
                action: `/tasks/${task.uuid}/toggle-complete`,
              });
              revalidator.revalidate();
            }}
          />
          <PageTitleText
            className={cn(task.completed && "text-gray-500 line-through")}
          >
            {task.title}
          </PageTitleText>
        </div>

        {/* Created Time */}
        <div className="flex items-center text-gray-600">
          <AccessTimeOutlined className="mr-2 h-5 w-5 text-blue-500" />
          <span className="grow text-sm font-medium">Created:</span>
          <Typography
            asChild
            color="secondary"
            variant="body2"
            className="ml-auto"
          >
            <AfterHydration>
              <span>
                {format(new Date(task.created), "ccc, MMM do, h:mm aaa")}
              </span>
            </AfterHydration>
          </Typography>
        </div>

        {/* Due Date */}
        <div className="flex items-center text-gray-600">
          <EventOutlined className="mr-2 h-5 w-5 text-red-500" />
          <span className="grow text-sm font-medium">Due:</span>
          <Typography
            asChild
            color="secondary"
            variant="body2"
            className="ml-auto"
          >
            <AfterHydration>
              <span>
                {task.dueDate
                  ? format(new Date(task.dueDate), "ccc, MMM do, h:mm aaa")
                  : "---"}
              </span>
            </AfterHydration>
          </Typography>
        </div>

        {/* Assignee */}
        {task.assignee && (
          <div className="flex items-center text-gray-600">
            <PeopleOutlined className="mr-2 h-5 w-5 text-gray-500" />
            <span className="grow text-sm font-medium">Assigned to:</span>
            <Typography
              asChild
              color="secondary"
              variant="body2"
              className="ml-auto"
            >
              <span>{task.assignee.name}</span>
            </Typography>
          </div>
        )}

        <Separator className="my-2" />

        <Typography variant="h3" color="primary">
          Parent note
        </Typography>
        {parentNote && !parentNote.isDeleted ? (
          <NoteCard
            compact
            note={parentNote as unknown as SerializeFrom<NoteResponse>}
            to={
              parentNote.status === ProcessingStatus.Scheduled
                ? {
                    pathname: `/notes/create/${parentNote.uuid}`,
                    search: `?noteID=${parentNote.uuid}`,
                  }
                : { pathname: `/notes/${parentNote.uuid}` }
            }
          />
        ) : (
          <Typography color="secondary" variant="body2">
            No parent note.
          </Typography>
        )}

        <Typography variant="h3" color="primary">
          Task description
        </Typography>
        {task.description ? (
          <Typography className="whitespace-pre-wrap">
            {task.description}
          </Typography>
        ) : (
          <Typography color="secondary" variant="body2">
            No task description.
          </Typography>
        )}
      </div>
    </SidebarV2>
  );
};
export default Route;
