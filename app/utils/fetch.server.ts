import { authenticator } from "~/auth/authenticator.server";
import { logError } from "./log.server";
import { redirect } from "@remix-run/node";

// Types
type FetchFn = typeof globalThis.fetch;
type FetchArguments = Parameters<FetchFn>;
export type ResponseWithPayload = Response & { payload?: unknown };

// Errors
/** Thrown if fetch fails before it can send a request all */
export class FailedToFetchError extends Error {
  constructor() {
    super("Failed to fetch");
    this.name = "FailedToFetchError";
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

/** Thrown if fetch fails to parse response */
export class FailedToParseResponseError extends Error {
  res: Response;
  constructor(res: Response) {
    super("Failed to parse response");
    this.name = "FailedToParseResponseError";
    this.res = res;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

/** Thrown on HTTP status codes over 400 (except 401). Contains parsed response */
export class ServerResponseError extends Error {
  res: ResponseWithPayload;
  constructor(res: ResponseWithPayload) {
    super("Server responded with error");
    this.name = "ServerResponseError";
    this.res = res;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

/** Thrown on HTTP status codes over 401. Contains parsed response */
export class ServerUnauthorizedResponseError extends Error {
  res: ResponseWithPayload;
  constructor(res: ResponseWithPayload) {
    super("Server responded with 401: Unauthorized");
    this.name = "ServerUnauthorizedResponseError";
    this.res = res;
    Object.setPrototypeOf(this, new.target.prototype);
  }
}

// Exports
/**
 * Drop-in replacement for fetch that adds error handling and response parsing
 * similar to Axios.
 *
 * parses based on response content-type:
 * - application/json     => res.json()
 * - multipart/form-data  => res.formData()
 * - everything else      => res.text()
 *
 * throws:
 * - FailedToFetchError         => could not send request at all
 * - FailedToParseResponseError => could not parse response
 * - ServerResponseError        => server responsed with an error status code
 */
export async function fetch(...args: FetchArguments) {
  // Fetch data
  let res: ResponseWithPayload;
  try {
    res = (await globalThis.fetch(...args)) as ResponseWithPayload;
  } catch (e) {
    throw e;
  }

  // Parse response based on content-type
  try {
    // We want to parse responses if possible, but if things go wrong, we want
    // to be able to parse the response wherever `callApi` was called from.
    // However, according to Fetch API spec, responses can only be parsed once.
    // To get around this, we clone the response before parsing, keeping the
    // original response body "unparsed".
    const clonedRes = res.clone();
    switch (res.headers.get("content-type")) {
      case "application/json":
        res.payload = await clonedRes.json();
        break;
      case "multipart/form-data":
        res.payload = await clonedRes.formData();
        break;
      case "text/html":
      default:
        res.payload = await clonedRes.text();
        break;
    }
  } catch {
    // Could not parse response based on content-type
    throw new FailedToParseResponseError(res);
  }

  // Handle error response
  if (res.status === 401) {
    throw new ServerUnauthorizedResponseError(res);
  }
  if (res.status >= 400) {
    throw new ServerResponseError(res);
  }

  // Valid response
  return res;
}

/** Just like `fetch`, but sets the Authorization header based on the user
 * session cookie */
export const fetchWithAuth = async (
  request: Request,
  // Match fetch API arguments
  input: FetchArguments[0],
  init?: FetchArguments[1]
) => {
  const userAuthSession = await authenticator.isAuthenticated(request, {
    failureRedirect: "/auth/login",
  });

  try {
    // When called with fetch(new Request())
    if (input instanceof Request) {
      return await fetch(input, {
        headers: {
          ...input.headers,
          // Node 19+ "socket hang up" fix: https://github.com/remix-run/remix/issues/4414
          connection: "keep-alive",
          Authorization: `Bearer ${userAuthSession.accessToken}`,
        },
      });
    }

    // When called with fetch('url', { ... })
    return await fetch(input, {
      ...init,
      headers: {
        ...init?.headers,
        // Node 19+ "socket hang up" fix: https://github.com/remix-run/remix/issues/4414
        connection: "keep-alive",
        Authorization: `Bearer ${userAuthSession.accessToken}`,
      },
    });
  } catch (error) {
    if (error instanceof ServerUnauthorizedResponseError) {
      logError("!!! fetchWithAuth 401", error);
      throw redirect("/auth/logout", 401);
    }
    throw error;
  }
};
