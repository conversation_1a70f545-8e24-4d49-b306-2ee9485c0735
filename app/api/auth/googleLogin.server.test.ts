// @vitest-environment node

import { googleLogin } from "./googleLogin.server";
import { AuthApi, UserLicenseType } from "../openapi/generated";
import { nonAuthenticatedConfigurationParameters } from "../openapi/configParams";
import { describe, it, expect, vi } from "vitest";

describe("googleLogin", () => {
  const mockAccessToken = "mockAccessToken";
  const mockParams = { basePath: "mockBasePath" };
  const mockResponse = {
    accessToken: "mockAccessToken",
    userProfile: {
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      uuid: "user123",
      orgId: "org123",
      isActive: true,
      status: "active",
      licenseType: UserLicenseType.Advisor,
    },
    refreshToken: "mockRefreshToken",
  };

  beforeEach(() => {
    vi.mock("../openapi/generated");
    vi.mock("../openapi/configParams");
    vi.mocked(nonAuthenticatedConfigurationParameters).mockResolvedValue(
      mockParams
    );
    vi.mocked(AuthApi.prototype.authGoogleSignin).mockResolvedValue(
      mockResponse
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should return user session on successful login", async () => {
    const result = await googleLogin({ accessToken: mockAccessToken });

    expect(result).toEqual({
      accessToken: mockResponse.accessToken,
      email: mockResponse.userProfile.email,
      firstName: mockResponse.userProfile.firstName,
      lastName: mockResponse.userProfile.lastName,
      refreshToken: mockResponse.refreshToken,
      userId: mockResponse.userProfile.uuid,
      orgId: mockResponse.userProfile.orgId,
    });

    expect(nonAuthenticatedConfigurationParameters).toHaveBeenCalled();
    expect(AuthApi.prototype.authGoogleSignin).toHaveBeenCalledWith({
      accessTokenAuthRequest: { accessToken: mockAccessToken },
    });
  });

  it("should log error and throw on failure", async () => {
    const mockError = new Error("mock error");
    vi.mocked(AuthApi.prototype.authGoogleSignin).mockRejectedValue(mockError);

    await expect(googleLogin({ accessToken: mockAccessToken })).rejects.toThrow(
      "Something went wrong"
    );
  });
});
