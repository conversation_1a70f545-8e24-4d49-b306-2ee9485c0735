// @vitest-environment node

import { microsoftLogin } from "./microsoftLogin.server";
import { AuthApi, UserLicenseType } from "../openapi/generated";
import { nonAuthenticatedConfigurationParameters } from "../openapi/configParams";
import { describe, it, expect, vi } from "vitest";

describe("microsoftLogin", () => {
  const mockAccessToken = "mockAccessToken";
  const mockParams = { basePath: "mockBasePath" };
  const mockResponse = {
    accessToken: "mockAccessToken",
    userProfile: {
      email: "<EMAIL>",
      firstName: "John",
      lastName: "Doe",
      uuid: "user123",
      orgId: "org123",
      isActive: true,
      status: "active",
      licenseType: UserLicenseType.Advisor,
    },
    refreshToken: "mockRefreshToken",
  };

  beforeEach(() => {
    vi.mock("../openapi/generated");
    vi.mock("../openapi/configParams");
    vi.mocked(nonAuthenticatedConfigurationParameters).mockResolvedValue(
      mockParams
    );
    vi.mocked(AuthApi.prototype.authMicrosoftSignin).mockResolvedValue(
      mockResponse
    );
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  it("should return user session on successful login", async () => {
    const result = await microsoftLogin({ accessToken: mockAccessToken });

    expect(result).toEqual({
      accessToken: mockResponse.accessToken,
      email: mockResponse.userProfile.email,
      firstName: mockResponse.userProfile.firstName,
      lastName: mockResponse.userProfile.lastName,
      refreshToken: mockResponse.refreshToken,
      userId: mockResponse.userProfile.uuid,
      orgId: mockResponse.userProfile.orgId,
    });

    expect(nonAuthenticatedConfigurationParameters).toHaveBeenCalled();
    expect(AuthApi.prototype.authMicrosoftSignin).toHaveBeenCalledWith({
      accessTokenAuthRequest: { accessToken: mockAccessToken },
    });
  });

  it("should log error and throw on failure", async () => {
    const mockError = new Error("mock error");
    vi.mocked(AuthApi.prototype.authMicrosoftSignin).mockRejectedValue(
      mockError
    );

    await expect(
      microsoftLogin({ accessToken: mockAccessToken })
    ).rejects.toThrow("Something went wrong");
  });
});
