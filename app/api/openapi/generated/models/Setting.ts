/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SelectOption } from './SelectOption';
import {
    SelectOptionFromJSON,
    SelectOptionFromJSONTyped,
    SelectOptionToJSON,
} from './SelectOption';
import type { SettingType } from './SettingType';
import {
    SettingTypeFromJSON,
    SettingTypeFromJSONTyped,
    SettingTypeToJSON,
} from './SettingType';

/**
 * 
 * @export
 * @interface Setting
 */
export interface Setting {
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    kind?: string;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    id: string;
    /**
     * 
     * @type {SettingType}
     * @memberof Setting
     */
    type: SettingType;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    tabType?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof Setting
     */
    isActive?: boolean | null;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    title?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    icon?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    redirectPath?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    value?: string | null;
    /**
     * 
     * @type {string}
     * @memberof Setting
     */
    clickIcon?: string | null;
    /**
     * 
     * @type {boolean}
     * @memberof Setting
     */
    editable?: boolean | null;
    /**
     * 
     * @type {boolean}
     * @memberof Setting
     */
    enabled?: boolean | null;
    /**
     * 
     * @type {Array<SelectOption>}
     * @memberof Setting
     */
    options?: Array<SelectOption> | null;
}



/**
 * Check if a given object implements the Setting interface.
 */
export function instanceOfSetting(value: object): value is Setting {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('type' in value) || value['type'] === undefined) return false;
    return true;
}

export function SettingFromJSON(json: any): Setting {
    return SettingFromJSONTyped(json, false);
}

export function SettingFromJSONTyped(json: any, ignoreDiscriminator: boolean): Setting {
    if (json == null) {
        return json;
    }
    return {
        
        'kind': json['kind'] == null ? undefined : json['kind'],
        'id': json['id'],
        'type': SettingTypeFromJSON(json['type']),
        'tabType': json['tabType'] == null ? undefined : json['tabType'],
        'isActive': json['isActive'] == null ? undefined : json['isActive'],
        'title': json['title'] == null ? undefined : json['title'],
        'icon': json['icon'] == null ? undefined : json['icon'],
        'redirectPath': json['redirectPath'] == null ? undefined : json['redirectPath'],
        'value': json['value'] == null ? undefined : json['value'],
        'clickIcon': json['clickIcon'] == null ? undefined : json['clickIcon'],
        'editable': json['editable'] == null ? undefined : json['editable'],
        'enabled': json['enabled'] == null ? undefined : json['enabled'],
        'options': json['options'] == null ? undefined : ((json['options'] as Array<any>).map(SelectOptionFromJSON)),
    };
}

export function SettingToJSON(value?: Setting | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'kind': value['kind'],
        'id': value['id'],
        'type': SettingTypeToJSON(value['type']),
        'tabType': value['tabType'],
        'isActive': value['isActive'],
        'title': value['title'],
        'icon': value['icon'],
        'redirectPath': value['redirectPath'],
        'value': value['value'],
        'clickIcon': value['clickIcon'],
        'editable': value['editable'],
        'enabled': value['enabled'],
        'options': value['options'] == null ? undefined : ((value['options'] as Array<any>).map(SelectOptionToJSON)),
    };
}

