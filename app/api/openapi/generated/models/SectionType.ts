/* tslint:disable */
/* eslint-disable */
/**
 * Z<PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const SectionType = {
    Modal: 'modal',
    SubSection: 'subSection'
} as const;
export type SectionType = typeof SectionType[keyof typeof SectionType];


export function instanceOfSectionType(value: any): boolean {
    for (const key in SectionType) {
        if (Object.prototype.hasOwnProperty.call(SectionType, key)) {
            if (SectionType[key as keyof typeof SectionType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function SectionTypeFromJSON(json: any): SectionType {
    return SectionTypeFromJSONTyped(json, false);
}

export function SectionTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionType {
    return json as SectionType;
}

export function SectionTypeToJSON(value?: SectionType | null): any {
    return value as any;
}

