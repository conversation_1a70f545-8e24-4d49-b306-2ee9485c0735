/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionDetailsDataInner } from './SectionDetailsDataInner';
import {
    SectionDetailsDataInnerFromJSON,
    SectionDetailsDataInnerFromJSONTyped,
    SectionDetailsDataInnerToJSON,
} from './SectionDetailsDataInner';
import type { SectionStaticContent } from './SectionStaticContent';
import {
    SectionStaticContentFromJSON,
    SectionStaticContentFromJSONTyped,
    SectionStaticContentToJSON,
} from './SectionStaticContent';
import type { SectionDetails } from './SectionDetails';
import {
    SectionDetailsFromJSON,
    SectionDetailsFromJSONTyped,
    SectionDetailsToJSON,
} from './SectionDetails';

/**
 * 
 * @export
 * @interface ResponseSettingsGetSettingsDetailsRoute
 */
export interface ResponseSettingsGetSettingsDetailsRoute {
    /**
     * 
     * @type {string}
     * @memberof ResponseSettingsGetSettingsDetailsRoute
     */
    label: string;
    /**
     * 
     * @type {Array<SectionDetailsDataInner>}
     * @memberof ResponseSettingsGetSettingsDetailsRoute
     */
    data: Array<SectionDetailsDataInner>;
    /**
     * 
     * @type {boolean}
     * @memberof ResponseSettingsGetSettingsDetailsRoute
     */
    showSaveButton?: boolean;
    /**
     * 
     * @type {string}
     * @memberof ResponseSettingsGetSettingsDetailsRoute
     */
    appTag: string;
}

/**
 * Check if a given object implements the ResponseSettingsGetSettingsDetailsRoute interface.
 */
export function instanceOfResponseSettingsGetSettingsDetailsRoute(value: object): value is ResponseSettingsGetSettingsDetailsRoute {
    if (!('label' in value) || value['label'] === undefined) return false;
    if (!('data' in value) || value['data'] === undefined) return false;
    if (!('appTag' in value) || value['appTag'] === undefined) return false;
    return true;
}

export function ResponseSettingsGetSettingsDetailsRouteFromJSON(json: any): ResponseSettingsGetSettingsDetailsRoute {
    return ResponseSettingsGetSettingsDetailsRouteFromJSONTyped(json, false);
}

export function ResponseSettingsGetSettingsDetailsRouteFromJSONTyped(json: any, ignoreDiscriminator: boolean): ResponseSettingsGetSettingsDetailsRoute {
    if (json == null) {
        return json;
    }
    return {
        
        'label': json['label'],
        'data': ((json['data'] as Array<any>).map(SectionDetailsDataInnerFromJSON)),
        'showSaveButton': json['showSaveButton'] == null ? undefined : json['showSaveButton'],
        'appTag': json['appTag'],
    };
}

export function ResponseSettingsGetSettingsDetailsRouteToJSON(value?: ResponseSettingsGetSettingsDetailsRoute | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'label': value['label'],
        'data': ((value['data'] as Array<any>).map(SectionDetailsDataInnerToJSON)),
        'showSaveButton': value['showSaveButton'],
        'appTag': value['appTag'],
    };
}

