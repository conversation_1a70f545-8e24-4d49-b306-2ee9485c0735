/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SelectOption
 */
export interface SelectOption {
    /**
     * 
     * @type {string}
     * @memberof SelectOption
     */
    value: string;
    /**
     * 
     * @type {string}
     * @memberof SelectOption
     */
    label: string;
}

/**
 * Check if a given object implements the SelectOption interface.
 */
export function instanceOfSelectOption(value: object): value is SelectOption {
    if (!('value' in value) || value['value'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SelectOptionFromJSON(json: any): SelectOption {
    return SelectOptionFromJSONTyped(json, false);
}

export function SelectOptionFromJSONTyped(json: any, ignoreDiscriminator: boolean): SelectOption {
    if (json == null) {
        return json;
    }
    return {
        
        'value': json['value'],
        'label': json['label'],
    };
}

export function SelectOptionToJSON(value?: SelectOption | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'value': value['value'],
        'label': value['label'],
    };
}

