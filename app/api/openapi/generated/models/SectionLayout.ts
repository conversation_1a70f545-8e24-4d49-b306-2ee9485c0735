/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const SectionLayout = {
    Grid: 'grid',
    Row: 'row',
    Column: 'column'
} as const;
export type SectionLayout = typeof SectionLayout[keyof typeof SectionLayout];


export function instanceOfSectionLayout(value: any): boolean {
    for (const key in SectionLayout) {
        if (Object.prototype.hasOwnProperty.call(SectionLayout, key)) {
            if (SectionLayout[key as keyof typeof SectionLayout] === value) {
                return true;
            }
        }
    }
    return false;
}

export function SectionLayoutFromJSON(json: any): SectionLayout {
    return SectionLayoutFromJSONTyped(json, false);
}

export function SectionLayoutFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionLayout {
    return json as SectionLayout;
}

export function SectionLayoutToJSON(value?: SectionLayout | null): any {
    return value as any;
}

