/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const SettingType = {
    RouterLink: 'routerLink',
    Link: 'link',
    Info: 'info',
    InterCom: 'interCom',
    Toggle: 'toggle',
    Select: 'select'
} as const;
export type SettingType = typeof SettingType[keyof typeof SettingType];


export function instanceOfSettingType(value: any): boolean {
    for (const key in SettingType) {
        if (Object.prototype.hasOwnProperty.call(SettingType, key)) {
            if (SettingType[key as keyof typeof SettingType] === value) {
                return true;
            }
        }
    }
    return false;
}

export function SettingTypeFromJSON(json: any): SettingType {
    return SettingTypeFromJSONTyped(json, false);
}

export function SettingTypeFromJSONTyped(json: any, ignoreDiscriminator: boolean): SettingType {
    return json as SettingType;
}

export function SettingTypeToJSON(value?: SettingType | null): any {
    return value as any;
}

