/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionItemAcknowledgementField } from './SectionItemAcknowledgementField';
import {
    SectionItemAcknowledgementFieldFromJSON,
    SectionItemAcknowledgementFieldFromJSONTyped,
    SectionItemAcknowledgementFieldToJSON,
} from './SectionItemAcknowledgementField';
import type { SectionItemFieldType } from './SectionItemFieldType';
import {
    SectionItemFieldTypeFromJSON,
    SectionItemFieldTypeFromJSONTyped,
    SectionItemFieldTypeToJSON,
} from './SectionItemFieldType';
import type { SectionItemLink } from './SectionItemLink';
import {
    SectionItemLinkFromJSON,
    SectionItemLinkFromJSONTyped,
    SectionItemLinkToJSON,
} from './SectionItemLink';
import type { SectionItemMultiChoiceField } from './SectionItemMultiChoiceField';
import {
    SectionItemMultiChoiceFieldFromJSON,
    SectionItemMultiChoiceFieldFromJSONTyped,
    SectionItemMultiChoiceFieldToJSON,
} from './SectionItemMultiChoiceField';
import type { LabeledEntity } from './LabeledEntity';
import {
    LabeledEntityFromJSON,
    LabeledEntityFromJSONTyped,
    LabeledEntityToJSON,
} from './LabeledEntity';
import type { SectionItemSingleChoiceField } from './SectionItemSingleChoiceField';
import {
    SectionItemSingleChoiceFieldFromJSON,
    SectionItemSingleChoiceFieldFromJSONTyped,
    SectionItemSingleChoiceFieldToJSON,
} from './SectionItemSingleChoiceField';
import type { SectionItemBooleanField } from './SectionItemBooleanField';
import {
    SectionItemBooleanFieldFromJSON,
    SectionItemBooleanFieldFromJSONTyped,
    SectionItemBooleanFieldToJSON,
} from './SectionItemBooleanField';
import type { SectionItemIntegrationCard } from './SectionItemIntegrationCard';
import {
    SectionItemIntegrationCardFromJSON,
    SectionItemIntegrationCardFromJSONTyped,
    SectionItemIntegrationCardToJSON,
} from './SectionItemIntegrationCard';
import type { SectionItemIntegrationCards } from './SectionItemIntegrationCards';
import {
    SectionItemIntegrationCardsFromJSON,
    SectionItemIntegrationCardsFromJSONTyped,
    SectionItemIntegrationCardsToJSON,
} from './SectionItemIntegrationCards';
import type { SectionItemTextField } from './SectionItemTextField';
import {
    SectionItemTextFieldFromJSON,
    SectionItemTextFieldFromJSONTyped,
    SectionItemTextFieldToJSON,
} from './SectionItemTextField';

/**
 * 
 * @export
 * @interface SectionDetailsDataInner
 */
export interface SectionDetailsDataInner {
    /**
     * 
     * @type {string}
     * @memberof SectionDetailsDataInner
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SectionDetailsDataInner
     */
    label: string;
    /**
     * 
     * @type {SectionItemFieldType}
     * @memberof SectionDetailsDataInner
     */
    kind?: SectionItemFieldType;
    /**
     * 
     * @type {string}
     * @memberof SectionDetailsDataInner
     */
    placeholder?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SectionDetailsDataInner
     */
    value?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof SectionDetailsDataInner
     */
    disabled?: boolean;
    /**
     * 
     * @type {Array<LabeledEntity>}
     * @memberof SectionDetailsDataInner
     */
    options?: Array<LabeledEntity>;
    /**
     * 
     * @type {string}
     * @memberof SectionDetailsDataInner
     */
    description?: string;
    /**
     * 
     * @type {string}
     * @memberof SectionDetailsDataInner
     */
    text?: string;
    /**
     * 
     * @type {string}
     * @memberof SectionDetailsDataInner
     */
    appTag?: string;
    /**
     * 
     * @type {Array<LabeledEntity>}
     * @memberof SectionDetailsDataInner
     */
    filters?: Array<LabeledEntity>;
    /**
     * 
     * @type {Array<SectionItemIntegrationCard>}
     * @memberof SectionDetailsDataInner
     */
    cards?: Array<SectionItemIntegrationCard>;
}



/**
 * Check if a given object implements the SectionDetailsDataInner interface.
 */
export function instanceOfSectionDetailsDataInner(value: object): value is SectionDetailsDataInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionDetailsDataInnerFromJSON(json: any): SectionDetailsDataInner {
    return SectionDetailsDataInnerFromJSONTyped(json, false);
}

export function SectionDetailsDataInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionDetailsDataInner {
    if (json == null) {
        return json;
    }
    return {
        
        'id': json['id'],
        'label': json['label'],
        'kind': json['kind'] == null ? undefined : SectionItemFieldTypeFromJSON(json['kind']),
        'placeholder': json['placeholder'] == null ? undefined : json['placeholder'],
        'value': json['value'] == null ? undefined : json['value'],
        'disabled': json['disabled'] == null ? undefined : json['disabled'],
        'options': json['options'] == null ? undefined : ((json['options'] as Array<any>).map(LabeledEntityFromJSON)),
        'description': json['description'] == null ? undefined : json['description'],
        'text': json['text'] == null ? undefined : json['text'],
        'appTag': json['appTag'] == null ? undefined : json['appTag'],
        'filters': json['filters'] == null ? undefined : ((json['filters'] as Array<any>).map(LabeledEntityFromJSON)),
        'cards': json['cards'] == null ? undefined : ((json['cards'] as Array<any>).map(SectionItemIntegrationCardFromJSON)),
    };
}

export function SectionDetailsDataInnerToJSON(value?: SectionDetailsDataInner | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'id': value['id'],
        'label': value['label'],
        'kind': SectionItemFieldTypeToJSON(value['kind']),
        'placeholder': value['placeholder'],
        'value': value['value'],
        'disabled': value['disabled'],
        'options': value['options'] == null ? undefined : ((value['options'] as Array<any>).map(LabeledEntityToJSON)),
        'description': value['description'],
        'text': value['text'],
        'appTag': value['appTag'],
        'filters': value['filters'] == null ? undefined : ((value['filters'] as Array<any>).map(LabeledEntityToJSON)),
        'cards': value['cards'] == null ? undefined : ((value['cards'] as Array<any>).map(SectionItemIntegrationCardToJSON)),
    };
}

