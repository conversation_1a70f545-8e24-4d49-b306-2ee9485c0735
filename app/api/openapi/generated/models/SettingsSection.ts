/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SettingsSectionSettingsInner } from './SettingsSectionSettingsInner';
import {
    SettingsSectionSettingsInnerFromJSON,
    SettingsSectionSettingsInnerFromJSONTyped,
    SettingsSectionSettingsInnerToJSON,
} from './SettingsSectionSettingsInner';
import type { SectionType } from './SectionType';
import {
    SectionTypeFromJSON,
    SectionTypeFromJSONTyped,
    SectionTypeToJSON,
} from './SectionType';
import type { SectionLayout } from './SectionLayout';
import {
    SectionLayoutFromJSON,
    SectionLayoutFromJSONTyped,
    SectionLayoutToJSON,
} from './SectionLayout';

/**
 * 
 * @export
 * @interface SettingsSection
 */
export interface SettingsSection {
    /**
     * 
     * @type {string}
     * @memberof SettingsSection
     */
    kind?: string;
    /**
     * 
     * @type {SectionType}
     * @memberof SettingsSection
     */
    sectionType?: SectionType;
    /**
     * 
     * @type {string}
     * @memberof SettingsSection
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SettingsSection
     */
    title: string;
    /**
     * 
     * @type {Array<SettingsSectionSettingsInner>}
     * @memberof SettingsSection
     */
    settings?: Array<SettingsSectionSettingsInner> | null;
    /**
     * 
     * @type {Array<string>}
     * @memberof SettingsSection
     */
    tabs?: Array<string> | null;
    /**
     * 
     * @type {SectionLayout}
     * @memberof SettingsSection
     */
    layout?: SectionLayout;
}



/**
 * Check if a given object implements the SettingsSection interface.
 */
export function instanceOfSettingsSection(value: object): value is SettingsSection {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    return true;
}

export function SettingsSectionFromJSON(json: any): SettingsSection {
    return SettingsSectionFromJSONTyped(json, false);
}

export function SettingsSectionFromJSONTyped(json: any, ignoreDiscriminator: boolean): SettingsSection {
    if (json == null) {
        return json;
    }
    return {
        
        'kind': json['kind'] == null ? undefined : json['kind'],
        'sectionType': json['section_type'] == null ? undefined : SectionTypeFromJSON(json['section_type']),
        'id': json['id'],
        'title': json['title'],
        'settings': json['settings'] == null ? undefined : ((json['settings'] as Array<any>).map(SettingsSectionSettingsInnerFromJSON)),
        'tabs': json['tabs'] == null ? undefined : json['tabs'],
        'layout': json['layout'] == null ? undefined : SectionLayoutFromJSON(json['layout']),
    };
}

export function SettingsSectionToJSON(value?: SettingsSection | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'kind': value['kind'],
        'section_type': SectionTypeToJSON(value['sectionType']),
        'id': value['id'],
        'title': value['title'],
        'settings': value['settings'] == null ? undefined : ((value['settings'] as Array<any>).map(SettingsSectionSettingsInnerToJSON)),
        'tabs': value['tabs'],
        'layout': SectionLayoutToJSON(value['layout']),
    };
}

