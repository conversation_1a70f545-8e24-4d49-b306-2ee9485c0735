/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


/**
 * 
 * @export
 */
export const MenuItemId = {
    MyAccount: 'my-account',
    Integrations: 'integrations',
    Settings: 'settings',
    ProfileDetails: 'profile-details',
    Admin: 'admin',
    UserImpersonation: 'user-impersonation'
} as const;
export type MenuItemId = typeof MenuItemId[keyof typeof MenuItemId];


export function instanceOfMenuItemId(value: any): boolean {
    for (const key in MenuItemId) {
        if (Object.prototype.hasOwnProperty.call(MenuItemId, key)) {
            if (MenuItemId[key as keyof typeof MenuItemId] === value) {
                return true;
            }
        }
    }
    return false;
}

export function MenuItemIdFromJSON(json: any): MenuItemId {
    return MenuItemIdFromJSONTyped(json, false);
}

export function MenuItemIdFromJSONTyped(json: any, ignoreDiscriminator: boolean): MenuItemId {
    return json as MenuItemId;
}

export function MenuItemIdToJSON(value?: MenuItemId | null): any {
    return value as any;
}

