/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface RedtailAuthRequest
 */
export interface RedtailAuthRequest {
    /**
     * 
     * @type {string}
     * @memberof RedtailAuthRequest
     */
    userId: string;
    /**
     * 
     * @type {string}
     * @memberof RedtailAuthRequest
     */
    username: string;
    /**
     * 
     * @type {string}
     * @memberof RedtailAuthRequest
     */
    password: string;
}

/**
 * Check if a given object implements the RedtailAuthRequest interface.
 */
export function instanceOfRedtailAuthRequest(value: object): value is RedtailAuthRequest {
    if (!('userId' in value) || value['userId'] === undefined) return false;
    if (!('username' in value) || value['username'] === undefined) return false;
    if (!('password' in value) || value['password'] === undefined) return false;
    return true;
}

export function RedtailAuthRequestFromJSON(json: any): RedtailAuthRequest {
    return RedtailAuthRequestFromJSONTyped(json, false);
}

export function RedtailAuthRequestFromJSONTyped(json: any, ignoreDiscriminator: boolean): RedtailAuthRequest {
    if (json == null) {
        return json;
    }
    return {
        
        'userId': json['user_id'],
        'username': json['username'],
        'password': json['password'],
    };
}

export function RedtailAuthRequestToJSON(value?: RedtailAuthRequest | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'user_id': value['userId'],
        'username': value['username'],
        'password': value['password'],
    };
}

