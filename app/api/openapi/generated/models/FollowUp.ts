/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON>lyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { FollowUpStatus } from './FollowUpStatus';
import {
    FollowUpStatusFromJSON,
    FollowUpStatusFromJSONTyped,
    FollowUpStatusToJSON,
} from './FollowUpStatus';

/**
 * 
 * @export
 * @interface FollowUp
 */
export interface FollowUp {
    /**
     * 
     * @type {string}
     * @memberof FollowUp
     */
    uuid: string;
    /**
     * 
     * @type {string}
     * @memberof FollowUp
     */
    title: string | null;
    /**
     * 
     * @type {string}
     * @memberof FollowUp
     */
    kind: string | null;
    /**
     * 
     * @type {object}
     * @memberof FollowUp
     */
    schema: object | null;
    /**
     * 
     * @type {object}
     * @memberof FollowUp
     */
    data: object | null;
    /**
     * The status of the follow-up.
     * @type {FollowUpStatus}
     * @memberof FollowUp
     */
    status: FollowUpStatus;
}



/**
 * Check if a given object implements the FollowUp interface.
 */
export function instanceOfFollowUp(value: object): value is FollowUp {
    if (!('uuid' in value) || value['uuid'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('kind' in value) || value['kind'] === undefined) return false;
    if (!('schema' in value) || value['schema'] === undefined) return false;
    if (!('data' in value) || value['data'] === undefined) return false;
    if (!('status' in value) || value['status'] === undefined) return false;
    return true;
}

export function FollowUpFromJSON(json: any): FollowUp {
    return FollowUpFromJSONTyped(json, false);
}

export function FollowUpFromJSONTyped(json: any, ignoreDiscriminator: boolean): FollowUp {
    if (json == null) {
        return json;
    }
    return {
        
        'uuid': json['uuid'],
        'title': json['title'],
        'kind': json['kind'],
        'schema': json['schema'],
        'data': json['data'],
        'status': FollowUpStatusFromJSON(json['status']),
    };
}

export function FollowUpToJSON(value?: FollowUp | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'uuid': value['uuid'],
        'title': value['title'],
        'kind': value['kind'],
        'schema': value['schema'],
        'data': value['data'],
        'status': FollowUpStatusToJSON(value['status']),
    };
}

