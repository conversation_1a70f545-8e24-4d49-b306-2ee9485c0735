/* tslint:disable */
/* eslint-disable */
/**
 * Zeplyn Internal API
 * Zeplyn first-party API, use by Zeplyn\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
/**
 * 
 * @export
 * @interface SectionStaticContent
 */
export interface SectionStaticContent {
    /**
     * 
     * @type {string}
     * @memberof SectionStaticContent
     */
    appTag: string;
    /**
     * 
     * @type {string}
     * @memberof SectionStaticContent
     */
    label: string;
}

/**
 * Check if a given object implements the SectionStaticContent interface.
 */
export function instanceOfSectionStaticContent(value: object): value is SectionStaticContent {
    if (!('appTag' in value) || value['appTag'] === undefined) return false;
    if (!('label' in value) || value['label'] === undefined) return false;
    return true;
}

export function SectionStaticContentFromJSON(json: any): SectionStaticContent {
    return SectionStaticContentFromJSONTyped(json, false);
}

export function SectionStaticContentFromJSONTyped(json: any, ignoreDiscriminator: boolean): SectionStaticContent {
    if (json == null) {
        return json;
    }
    return {
        
        'appTag': json['appTag'],
        'label': json['label'],
    };
}

export function SectionStaticContentToJSON(value?: SectionStaticContent | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'appTag': value['appTag'],
        'label': value['label'],
    };
}

