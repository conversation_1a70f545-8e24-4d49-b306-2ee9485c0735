/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

import { mapValues } from '../runtime';
import type { SectionType } from './SectionType';
import {
    SectionTypeFromJSON,
    SectionTypeFromJSONTyped,
    SectionTypeToJSON,
} from './SectionType';
import type { SectionLayout } from './SectionLayout';
import {
    SectionLayoutFromJSON,
    SectionLayoutFromJSONTyped,
    SectionLayoutToJSON,
} from './SectionLayout';
import type { SelectOption } from './SelectOption';
import {
    SelectOptionFromJSON,
    SelectOptionFromJSONTyped,
    SelectOptionToJSON,
} from './SelectOption';
import type { Setting } from './Setting';
import {
    Setting<PERSON>romJSON,
    SettingFromJSONTyped,
    SettingToJSON,
} from './Setting';
import type { SettingsSection } from './SettingsSection';
import {
    SettingsSectionFromJSON,
    SettingsSectionFromJSONTyped,
    SettingsSectionToJSON,
} from './SettingsSection';
import type { SettingType } from './SettingType';
import {
    SettingTypeFromJSON,
    SettingTypeFromJSONTyped,
    SettingTypeToJSON,
} from './SettingType';

/**
 * 
 * @export
 * @interface SettingsSectionSettingsInner
 */
export interface SettingsSectionSettingsInner {
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    kind?: string;
    /**
     * 
     * @type {SectionType}
     * @memberof SettingsSectionSettingsInner
     */
    sectionType?: SectionType;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    id: string;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    title: string;
    /**
     * 
     * @type {Array<SettingsSectionSettingsInner>}
     * @memberof SettingsSectionSettingsInner
     */
    settings?: Array<SettingsSectionSettingsInner>;
    /**
     * 
     * @type {Array<string>}
     * @memberof SettingsSectionSettingsInner
     */
    tabs?: Array<string>;
    /**
     * 
     * @type {SectionLayout}
     * @memberof SettingsSectionSettingsInner
     */
    layout?: SectionLayout;
    /**
     * 
     * @type {SettingType}
     * @memberof SettingsSectionSettingsInner
     */
    type: SettingType;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    tabType?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SettingsSectionSettingsInner
     */
    isActive?: boolean;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    icon?: string;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    redirectPath?: string;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    value?: string;
    /**
     * 
     * @type {string}
     * @memberof SettingsSectionSettingsInner
     */
    clickIcon?: string;
    /**
     * 
     * @type {boolean}
     * @memberof SettingsSectionSettingsInner
     */
    editable?: boolean;
    /**
     * 
     * @type {boolean}
     * @memberof SettingsSectionSettingsInner
     */
    enabled?: boolean;
    /**
     * 
     * @type {Array<SelectOption>}
     * @memberof SettingsSectionSettingsInner
     */
    options?: Array<SelectOption>;
}



/**
 * Check if a given object implements the SettingsSectionSettingsInner interface.
 */
export function instanceOfSettingsSectionSettingsInner(value: object): value is SettingsSectionSettingsInner {
    if (!('id' in value) || value['id'] === undefined) return false;
    if (!('title' in value) || value['title'] === undefined) return false;
    if (!('type' in value) || value['type'] === undefined) return false;
    return true;
}

export function SettingsSectionSettingsInnerFromJSON(json: any): SettingsSectionSettingsInner {
    return SettingsSectionSettingsInnerFromJSONTyped(json, false);
}

export function SettingsSectionSettingsInnerFromJSONTyped(json: any, ignoreDiscriminator: boolean): SettingsSectionSettingsInner {
    if (json == null) {
        return json;
    }
    return {
        
        'kind': json['kind'] == null ? undefined : json['kind'],
        'sectionType': json['section_type'] == null ? undefined : SectionTypeFromJSON(json['section_type']),
        'id': json['id'],
        'title': json['title'],
        'settings': json['settings'] == null ? undefined : ((json['settings'] as Array<any>).map(SettingsSectionSettingsInnerFromJSON)),
        'tabs': json['tabs'] == null ? undefined : json['tabs'],
        'layout': json['layout'] == null ? undefined : SectionLayoutFromJSON(json['layout']),
        'type': SettingTypeFromJSON(json['type']),
        'tabType': json['tabType'] == null ? undefined : json['tabType'],
        'isActive': json['isActive'] == null ? undefined : json['isActive'],
        'icon': json['icon'] == null ? undefined : json['icon'],
        'redirectPath': json['redirectPath'] == null ? undefined : json['redirectPath'],
        'value': json['value'] == null ? undefined : json['value'],
        'clickIcon': json['clickIcon'] == null ? undefined : json['clickIcon'],
        'editable': json['editable'] == null ? undefined : json['editable'],
        'enabled': json['enabled'] == null ? undefined : json['enabled'],
        'options': json['options'] == null ? undefined : ((json['options'] as Array<any>).map(SelectOptionFromJSON)),
    };
}

export function SettingsSectionSettingsInnerToJSON(value?: SettingsSectionSettingsInner | null): any {
    if (value == null) {
        return value;
    }
    return {
        
        'kind': value['kind'],
        'section_type': SectionTypeToJSON(value['sectionType']),
        'id': value['id'],
        'title': value['title'],
        'settings': value['settings'] == null ? undefined : ((value['settings'] as Array<any>).map(SettingsSectionSettingsInnerToJSON)),
        'tabs': value['tabs'],
        'layout': SectionLayoutToJSON(value['layout']),
        'type': SettingTypeToJSON(value['type']),
        'tabType': value['tabType'],
        'isActive': value['isActive'],
        'icon': value['icon'],
        'redirectPath': value['redirectPath'],
        'value': value['value'],
        'clickIcon': value['clickIcon'],
        'editable': value['editable'],
        'enabled': value['enabled'],
        'options': value['options'] == null ? undefined : ((value['options'] as Array<any>).map(SelectOptionToJSON)),
    };
}

