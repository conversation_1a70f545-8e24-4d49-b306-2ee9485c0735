/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ApiRoutersClientClientResponse,
  ClientRecapResponse,
  HTTPValidationError,
} from '../models/index';
import {
    ApiRoutersClientClientResponseFromJSON,
    ApiRoutersClientClientResponseToJSON,
    ClientRecapResponseFromJSON,
    ClientRecapResponseToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
} from '../models/index';

export interface ClientGenerateClientRecapRequest {
    clientId: string;
}

export interface ClientGetClientRequest {
    clientId: string;
}

/**
 * 
 */
export class C<PERSON><PERSON><PERSON> extends runtime.BaseAPI {

    /**
     * Initiate the generation of a client recap for a specific client
     * Generate a client recap
     */
    async clientGenerateClientRecapRaw(requestParameters: ClientGenerateClientRecapRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ClientRecapResponse>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling clientGenerateClientRecap().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/client/{client_id}/generate-recap`.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ClientRecapResponseFromJSON(jsonValue));
    }

    /**
     * Initiate the generation of a client recap for a specific client
     * Generate a client recap
     */
    async clientGenerateClientRecap(requestParameters: ClientGenerateClientRecapRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ClientRecapResponse> {
        const response = await this.clientGenerateClientRecapRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * Get detailed information about a specific client including the client recap, notes, and action items
     * Get client details
     */
    async clientGetClientRaw(requestParameters: ClientGetClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ApiRoutersClientClientResponse>> {
        if (requestParameters['clientId'] == null) {
            throw new runtime.RequiredError(
                'clientId',
                'Required parameter "clientId" was null or undefined when calling clientGetClient().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/client/{client_id}`.replace(`{${"client_id"}}`, encodeURIComponent(String(requestParameters['clientId']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ApiRoutersClientClientResponseFromJSON(jsonValue));
    }

    /**
     * Get detailed information about a specific client including the client recap, notes, and action items
     * Get client details
     */
    async clientGetClient(requestParameters: ClientGetClientRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ApiRoutersClientClientResponse> {
        const response = await this.clientGetClientRaw(requestParameters, initOverrides);
        return await response.value();
    }

}
