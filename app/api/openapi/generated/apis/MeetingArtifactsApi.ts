/* tslint:disable */
/* eslint-disable */
/**
 * <PERSON><PERSON>lyn Internal API
 * Zeplyn first-party API, use by <PERSON><PERSON><PERSON>\'s web and mobile apps.
 *
 * The version of the OpenAPI document: 1.0.0
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */


import * as runtime from '../runtime';
import type {
  ClientInteraction,
  HTTPValidationError,
  MeetingSummaryEmailTemplate,
  MeetingType,
} from '../models/index';
import {
    ClientInteractionFromJSON,
    ClientInteractionToJSON,
    HTTPValidationErrorFromJSON,
    HTTPValidationErrorToJSON,
    MeetingSummaryEmailTemplateFromJSON,
    MeetingSummaryEmailTemplateToJSON,
    MeetingTypeFromJSON,
    MeetingTypeToJSON,
} from '../models/index';

export interface MeetingArtifactsGetClientInteractionRequest {
    interactionUuid: string;
}

export interface MeetingArtifactsUpdateFollowUpRequest {
    followUpId: string;
    body: object;
}

/**
 * 
 */
export class MeetingArtifacts<PERSON>pi extends runtime.BaseAPI {

    /**
     * Get Client Interaction
     */
    async meetingArtifactsGetClientInteractionRaw(requestParameters: MeetingArtifactsGetClientInteractionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<ClientInteraction>> {
        if (requestParameters['interactionUuid'] == null) {
            throw new runtime.RequiredError(
                'interactionUuid',
                'Required parameter "interactionUuid" was null or undefined when calling meetingArtifactsGetClientInteraction().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/meeting_artifact/client_interaction/{interaction_uuid}`.replace(`{${"interaction_uuid"}}`, encodeURIComponent(String(requestParameters['interactionUuid']))),
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => ClientInteractionFromJSON(jsonValue));
    }

    /**
     * Get Client Interaction
     */
    async meetingArtifactsGetClientInteraction(requestParameters: MeetingArtifactsGetClientInteractionRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<ClientInteraction> {
        const response = await this.meetingArtifactsGetClientInteractionRaw(requestParameters, initOverrides);
        return await response.value();
    }

    /**
     * List Client Interactions
     */
    async meetingArtifactsListClientInteractionsRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<ClientInteraction>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/meeting_artifact/client_interaction`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(ClientInteractionFromJSON));
    }

    /**
     * List Client Interactions
     */
    async meetingArtifactsListClientInteractions(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<ClientInteraction>> {
        const response = await this.meetingArtifactsListClientInteractionsRaw(initOverrides);
        return await response.value();
    }

    /**
     * Meeting Summary Email Templates
     */
    async meetingArtifactsMeetingSummaryEmailTemplatesRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<MeetingSummaryEmailTemplate>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/meeting_artifact/meeting_summary_email_templates`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(MeetingSummaryEmailTemplateFromJSON));
    }

    /**
     * Meeting Summary Email Templates
     */
    async meetingArtifactsMeetingSummaryEmailTemplates(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<MeetingSummaryEmailTemplate>> {
        const response = await this.meetingArtifactsMeetingSummaryEmailTemplatesRaw(initOverrides);
        return await response.value();
    }

    /**
     * Meeting Types
     */
    async meetingArtifactsMeetingTypesRaw(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<Array<MeetingType>>> {
        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/meeting_artifact/meeting_types`,
            method: 'GET',
            headers: headerParameters,
            query: queryParameters,
        }, initOverrides);

        return new runtime.JSONApiResponse(response, (jsonValue) => jsonValue.map(MeetingTypeFromJSON));
    }

    /**
     * Meeting Types
     */
    async meetingArtifactsMeetingTypes(initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<Array<MeetingType>> {
        const response = await this.meetingArtifactsMeetingTypesRaw(initOverrides);
        return await response.value();
    }

    /**
     * Updates the data stored in a meeting follow up.
     * Update Follow Up
     */
    async meetingArtifactsUpdateFollowUpRaw(requestParameters: MeetingArtifactsUpdateFollowUpRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<runtime.ApiResponse<void>> {
        if (requestParameters['followUpId'] == null) {
            throw new runtime.RequiredError(
                'followUpId',
                'Required parameter "followUpId" was null or undefined when calling meetingArtifactsUpdateFollowUp().'
            );
        }

        if (requestParameters['body'] == null) {
            throw new runtime.RequiredError(
                'body',
                'Required parameter "body" was null or undefined when calling meetingArtifactsUpdateFollowUp().'
            );
        }

        const queryParameters: any = {};

        const headerParameters: runtime.HTTPHeaders = {};

        headerParameters['Content-Type'] = 'application/json';

        if (this.configuration && this.configuration.accessToken) {
            const token = this.configuration.accessToken;
            const tokenString = await token("HTTPBearer", []);

            if (tokenString) {
                headerParameters["Authorization"] = `Bearer ${tokenString}`;
            }
        }
        const response = await this.request({
            path: `/api/v2/meeting_artifact/follow-up/{follow_up_id}`.replace(`{${"follow_up_id"}}`, encodeURIComponent(String(requestParameters['followUpId']))),
            method: 'POST',
            headers: headerParameters,
            query: queryParameters,
            body: requestParameters['body'] as any,
        }, initOverrides);

        return new runtime.VoidApiResponse(response);
    }

    /**
     * Updates the data stored in a meeting follow up.
     * Update Follow Up
     */
    async meetingArtifactsUpdateFollowUp(requestParameters: MeetingArtifactsUpdateFollowUpRequest, initOverrides?: RequestInit | runtime.InitOverrideFunction): Promise<void> {
        await this.meetingArtifactsUpdateFollowUpRaw(requestParameters, initOverrides);
    }

}
