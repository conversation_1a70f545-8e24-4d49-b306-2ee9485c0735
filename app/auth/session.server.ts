import { createCookieSessionStorage } from "@remix-run/node";

// Helpers
export const createAuthSessionStorage = () => {
  if (!process.env.ZEPLYN_SESSION_COOKIE_SECRET) {
    throw Error("process.env.ZEPLYN_SESSION_COOKIE_SECRET not set");
  }

  return createCookieSessionStorage({
    cookie: {
      httpOnly: true,
      name: process.env.ZEPLYN_SESSION_COOKIE_NAME,
      path: "/",
      sameSite: "lax",
      secrets: [process.env.ZEPLYN_SESSION_COOKIE_SECRET],
      secure: true,
    },
  });
};

// Exports
export const authSessionStorage = createAuthSessionStorage();
