import { describe, it, expect, vi } from "vitest";
import { loader } from "./root";
import { cookieHeadersForFlags } from "~/utils/flagsInCookies";

vi.mock("~/utils/flagsInCookies", () => ({
  cookieHeadersForFlags: vi.fn(),
}));

describe("loader", () => {
  it("returns environment variables and headers", async () => {
    const fakeRequest = new Request("http://localhost:3000");
    const fakeHeaders = { "Set-Cookie": "test-cookie" };

    vi.mocked(cookieHeadersForFlags).mockResolvedValue(fakeHeaders);

    process.env.ZEPLYN_ENV = "test-env";
    process.env.ZEPLYN_DATADOG_APPLICATION_ID = "test-application-id";
    process.env.ZEPLYN_DATADOG_CLIENT_TOKEN = "test-client-token";
    process.env.ZEPLYN_APP_VERSION = "1.0.0";
    process.env.ZEPLYN_RELEASE_VERSION = "v1.0.0";
    process.env.ZEPLYN_HOST = "http://localhost";

    const response = await loader({
      request: fakeRequest,
      params: {},
      context: {},
    });
    const data = await response.json();

    expect(response.headers.getSetCookie()).toEqual(["test-cookie"]);
    expect(data).toEqual({
      environment: "test-env",
      datadogApplicationID: "test-application-id",
      datadogClientToken: "test-client-token",
      appVersion: "1.0.0",
      releaseVersion: "v1.0.0",
      host: "http://localhost",
    });
    expect(cookieHeadersForFlags).toHaveBeenCalledWith(fakeRequest);
  });

  it("handles missing environment variables gracefully", async () => {
    const fakeRequest = new Request("http://localhost:3000");
    const fakeHeaders = { "Set-Cookie": "test-cookie" };

    vi.mocked(cookieHeadersForFlags).mockResolvedValue(fakeHeaders);

    delete process.env.ZEPLYN_ENV;
    delete process.env.ZEPLYN_DATADOG_APPLICATION_ID;
    delete process.env.ZEPLYN_DATADOG_CLIENT_TOKEN;
    delete process.env.ZEPLYN_APP_VERSION;
    delete process.env.ZEPLYN_RELEASE_VERSION;
    delete process.env.ZEPLYN_HOST;

    const response = await loader({
      request: fakeRequest,
      params: {},
      context: {},
    });
    const data = await response.json();

    expect(response.headers.getSetCookie()).toEqual(["test-cookie"]);
    expect(data).toEqual({
      environment: undefined,
      datadogApplicationID: undefined,
      datadogClientToken: undefined,
      appVersion: undefined,
      releaseVersion: undefined,
      host: undefined,
    });
    expect(cookieHeadersForFlags).toHaveBeenCalledWith(fakeRequest);
  });
});
