import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "~/@shadcn/utils";

export const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md font-medium transition-colors disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default:
          "bg-primary text-primary-foreground shadow hover:bg-foreground",
        destructive:
          "bg-destructive text-primary-foreground shadow-sm hover:bg-destructive/90",
        success:
          "bg-success text-primary-foreground shadow-sm hover:bg-success/90",
        outline:
          "border border-border bg-background shadow hover:border-foreground hover:bg-accent hover:text-accent-foreground",
        outline_primary:
          "border border-primary bg-background text-primary shadow hover:border-foreground hover:bg-accent hover:text-accent-foreground",
        secondary:
          "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        "ghost-destructive":
          "text-destructive hover:bg-destructive-foreground hover:text-destructive",
        "ghost-success":
          "text-success hover:bg-success-foreground hover:text-success",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default:
          "h-9 min-h-9 gap-1 rounded-lg px-2 text-base [&>svg]:h-6 [&>svg]:w-6",
        sm: "h-8 min-h-8 rounded-md px-3 text-xs",
        lg: "h-16 min-h-16 gap-2 rounded-2xl px-4 py-2 text-base [&>svg]:h-6 [&>svg]:w-6",
        "icon-xs": "h-7 w-7 shrink-0 rounded-lg p-0 [&>svg]:h-6 [&>svg]:w-6",
        "icon-sm": "h-9 w-9 shrink-0 rounded-md p-0 [&>svg]:h-5 [&>svg]:w-5",
        icon: "h-[3.25rem] w-[3.25rem] shrink-0",
        "icon-lg": "h-20 w-20 shrink-0",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export type ButtonProps = React.ButtonHTMLAttributes<HTMLButtonElement> &
  VariantProps<typeof buttonVariants> & { asChild?: boolean };

export const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button";
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    );
  }
);
Button.displayName = "Button";
