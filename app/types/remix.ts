import { useLoaderData } from "@remix-run/react";

/**
 * Custom SerializeFrom type to replace deprecated @remix-run/node version
 * Based on: https://remix.run/docs/en/main/start/future-flags#deprecations
 *
 * This type represents the serialized form of data returned from loaders,
 * where Date objects become strings and other non-serializable types are transformed.
 */
export type SerializeFrom<T> = ReturnType<typeof useLoaderData<T>>;
